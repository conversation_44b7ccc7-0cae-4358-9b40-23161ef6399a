
Ext.define("layanan.WorkspaceController", {
	extend : "Ext.app.ViewController",
	alias : "controller.layanan-workspace",
	onReload : function () {
		var c = this,
		a = c.getView(),
		b = c.getViewModel();
		a.setLoading(true);
		(new Ext.util.DelayedTask(function() {
			if(data.model) {
				data.model.Kunjungan.load(b.get("kunjungan").get('NOMOR'), {
					callback: function(rec, opr, success) {
						if(success) {
							a.load(b.get("pasien"), b.get("pendaftaran"), rec);
						}
						a.setLoading(false);
					}
				});
			}
		})).delay(1500);
	},
	onFinal : function (b) {
		var d = this,
		c = d.getViewModel(),
		view = d.getView(),
		e = c.get("kunjungan"),
		ref = e.get("REFERENSI"),
		lfs = c.get('farmasistore'),
		lfk = c.get('farmasikardekstore'),
		fe = c.get('farmasiexpiredstore'),
		intrvn = c.get('farmasiintravenastore'),
		perj = c.get('perjanjianstore'),
		pend = c.get('pendaftaran'),
		paspul = ref.PASIEN_KELUAR,
		idRadiologi = ref.RUANGAN.ID,
		jnsKunjungan = ref.RUANGAN.JENIS_KUNJUNGAN,
		pac = ref.PACS,
		record = {},
		a = e ? e.get("STATUS") : 0;
		/* console.log(e); */
		if (a == 0) {
			return
		}
		if(pend.get('STATUS') == 2 && jnsKunjungan == 11){
			view.notifyMessage("Tagihan Kasir Sudah Final, Untuk Final Kunjungan Buka Tagihan Dahulu");
			return;
		}
		Ext.Msg.show({
			title : "Final Kunjungan",
			message : ref.RUANGAN.ID == '105050156'?"Anda yakin ingin menyelesaikan / memfinalkan kunjungan ini ?<br><br><label><input type='checkbox' checked=\"checked\" id='wa_pasien'/> Kirim whatsapp ke pasien? (Hanya Pasien Rawat Jalan)</label>":"Anda yakin ingin menyelesaikan / memfinalkan kunjungan ini ?",
			buttons : Ext.Msg.YESNO,
			icon : Ext.Msg.QUESTION,
			animateTarget : b,
			fn : function (f) {
				if (f === "yes") {
					
					e.showError = true;
					e.animateTarget = b;
					e.scope = d;
					e.set("STATUS", 2);
					if(jnsKunjungan == 11){
						var kirimWA = 0;
						var waCheckbox = document.getElementById('wa_pasien');
						if (waCheckbox && waCheckbox.checked) {
							kirimWA = 1;
						}
						e.set("KIRIMWA", kirimWA);
					}
				//	console.log(e);
					e.save({
						callback : function (h, g, i) {
							if (i) {
								d.getView().notifyMessage("Kunjungan telah difinalkan");
								d.onReload();
							} else {
								// Kembalikan status ke nilai semula jika save gagal
								e.set("STATUS", a);
								e.set("KIRIMWA", null); // Reset KIRIMWA jika ada
								d.getView().notifyMessage("Gagal memfinalkan kunjungan", "danger-red");
							}
						}
					});

					if(jnsKunjungan == 11){

								lfs.queryParams = {
									OBATTERAKHIR : e.get("NOMOR")
								};
								lfs.load(function(n,h,o){
									if(o){
										view.openDialog("", true, '80%', '70%', {
											//region : "center",
											bodyPadding : 5,
											flex : 1,
											header : {
												style: {
													background: '#E9B12F'
												}
											},
											xtype : "farmasi-obatterakhir-workspace",
											showCloseButton: true,
											//ui : 'danger-red'
											}, function(onEvent, win) {
												var h = win.down("farmasi-obatterakhir-workspace");
												h.load(n);
												h.on('simpansuccess', function(rec, opr, success) {
													win.close();
													//if(idRadiologi == '105050102'){
														//delete lfs.queryParams.OBATTERAKHIR;
													if(idRadiologi == '105050102'){
															// lfk.queryParams = {
															// 	CEKKARDEK : e.get("NOMOR")
															// };
															// lfk.load(function(n,h,o){
															// 	if(o){
															// 		if(n.length >= 1){
															// 			for ( i=0; i<n.length; i++) {
															// 				record[i] = Ext.create("data.model.KardekRawat", n[i].data);
															// 				record[i].showError = true;
															// 				record[i].save();
															// 			}
															// 		}
															// 	}
															// });

															intrvn.queryParams = {
																INTRAVENA : e.get("NOMOR")
															};
															intrvn.load(function(p,q,r){
																if(r){
																	if(p.length >= 1){
																		var records = {};
																		for ( i=0; i<p.length; i++) {
																			records[i] = Ext.create("data.model.PemberianCairanIntravena", p[i].data);
																			records[i].showError = true;
																			records[i].save();
																		}
																	}
																}
															});
													}
														
													//}
													
												});
												
											});
									}else{
										//var record = {};
										/*fe.queryParams = {
											OBATEXPIRED : e.get("NOMOR")
										};
										fe.load(function(n,h,o){
											if(o){
												view.openDialog("", true, '70%', '40%', {
													bodyPadding : 5,
													flex : 1,
													header : {
														style: {
															background: '#F3240F'
														}
													},
													xtype : "farmasi-obatexpired-workspace",
													showCloseButton: true,
													}, function(onEvent, win) {
														var h = win.down("farmasi-obatexpired-workspace");
														h.load(n);
														h.on('simpansuccess', function(rec, opr, success) {
															win.close();
															//d.onReload();
														});
														
													});
											}
										});	*/

											
											
											// d.integrasi = view.app.xitr('ID', 1);
											// if(d.integrasi){
											// 	d.service = Ext.create(d.integrasi.NAMA_KLAS, {});
											// }
											// perj.queryParams = {
											// 	NOMR : pend.get('NORM'),
											// 	TANGGAL : Ext.Date.format(view.getSysdate(), "Y-m-d")
											// };

											// perj.load(function(n,h,o){
											// 	if(o){
											// 		if(n.length >= 1){
											// 			d.service.antreanTerimaResep({
											// 				kodebooking : n[0].get('ID')//734931
											// 				,taskid : 7
											// 				,waktu : Date.now()
											// 			});
											// 		}
											// 	}
											// });

											if(idRadiologi == '105050102'){
												// lfk.queryParams = {
												// 	CEKKARDEK : e.get("NOMOR")
												// };
												// lfk.load(function(n,h,o){
												// 	if(o){
												// 		if(n.length >= 1){
												// 			//var record = {};
												// 			for ( i=0; i<n.length; i++) {
												// 				record[i] = Ext.create("data.model.KardekRawat", n[i].data);
												// 				record[i].showError = true;
												// 				record[i].save();
												// 			}
												// 		}
												// 	}
												// });
	
	
												intrvn.queryParams = {
													INTRAVENA : e.get("NOMOR")
												};
												intrvn.load(function(p,q,r){
													if(r){
														if(p.length >= 1){
															var records = {};
															for ( i=0; i<p.length; i++) {
																records[i] = Ext.create("data.model.PemberianCairanIntravena", p[i].data);
																records[i].showError = true;
																records[i].save();
															}
														}
													}
												});
											}
									}
								});
							
					}else{
							// e.showError = true;
							// e.animateTarget = b;
							// e.scope = d;
							// e.set("STATUS", 2);
							// e.save({
							// 	callback : function (h, g, i) {
							// 		if (i) {
							// 			d.getView().notifyMessage("Kunjungan telah difinalkan");
							// 			d.onReload();
							// 		} else {
							// 			e.set("STATUS", a)
							// 		}
							// 	}
							// });
							
							var adawilayah = ref.PENDAFTARAN.REFERENSI.PASIEN.WILAYAH,
							_provinsi = '',
							_kota = '',
							_kecamatan = '',
							_kelurahan = '';
							
							if(adawilayah != null && adawilayah != '3173' ){
								_provinsi = ref.PENDAFTARAN.REFERENSI.PASIEN.REFERENSI.PROVINSI.DESKRIPSI;
								_kota = ref.PENDAFTARAN.REFERENSI.PASIEN.REFERENSI.KOTA.DESKRIPSI;
								_kecamatan = ref.PENDAFTARAN.REFERENSI.PASIEN.REFERENSI.KECAMATAN.DESKRIPSI;
								_kelurahan = ref.PENDAFTARAN.REFERENSI.PASIEN.REFERENSI.WILAYAH.DESKRIPSI;
							}
									
							if(pac){
								if(idRadiologi == '105100101' && jnsKunjungan == 5 ){
									var modpacs = Ext.create("data.model.OrderRis",pac);				
									modpacs.set('FLAG',1);
									modpacs.save({
										callback: function(rec, opt, success) {
											if(success) {
												Ext.toast({
													html : "Order Pacs Terkirim",
													headerPosition : "left",
													style : "padding: 7px 0px 0px 4px",
													border : false,
													header : {
														width : 13
													},
													ui : "danger-blue",
													align : "t"
												});
											}
										}
									}); 
								} 
							}else{
								if(idRadiologi == '105100101' && jnsKunjungan == 5 ){
								var	 modpacs = Ext.create("data.model.OrderRis",{
														ID : e.get('ID'),
														NOKUN : e.get('NOMOR'),
														HEADERALATRS : 'RumahSakitKankerDharmais',
														NORM : ref.PENDAFTARAN.NORM,
														NOPEN : e.get('NOPEN'),
														NAMAPASIEN : ref.PENDAFTARAN.REFERENSI.PASIEN.NAMA,
														TANGGALLAHIR : ref.PENDAFTARAN.REFERENSI.PASIEN.TANGGAL_LAHIR,
														JK : ref.PENDAFTARAN.REFERENSI.PASIEN.REFERENSI.JENISKELAMIN.ID,
														ALAMAT : ref.PENDAFTARAN.REFERENSI.PASIEN.ALAMAT,
														PROVINSI : _provinsi,
														KOTA : _kota,
														KECAMATAN : _kecamatan,
														KELURAHAN : _kelurahan,
														KDINSTANSIPENERIMA : idRadiologi,
														NMINSTANSIPENERIMA : 'InstalasiRadioDiagnostik',
														FLAG : 1
													});
					
									modpacs.save({
										callback: function(rec, opt, success) {
											if(success) {
												Ext.toast({
													html : "Order Pacs Terkirim",
													headerPosition : "left",
													style : "padding: 7px 0px 0px 4px",
													border : false,
													header : {
														width : 13
													},
													ui : "danger-blue",
													align : "t"
												});
											}
										}
									}); 
								} 
								
							} 		
								
							if(paspul){
								var pulang = Ext.create("data.model.PasienPulang",paspul);
								pulang.set("STATUS",1);
								pulang.save({
											callback: function(rec, opt, success) {
												if(success) {
													Ext.toast({
														html : "Pasien Telah Pulang",
														headerPosition : "left",
														style : "padding: 7px 0px 0px 4px",
														border : false,
														header : {
															width : 13
														},
														ui : "danger-blue",
														align : "t"
													});
												}
											}
										}); 
							}

					}	
				} else {
					e.set("STATUS", a)
				}
			}
		})
	},
	onBatalKunjungan: function(t) {
		var me = this,
			view = me.getView(),
			vm = me.getViewModel(),
			kjgn = vm.get('kunjungan'),
			ref = kjgn.get('REFERENSI'),
			jnsKunjungan = ref.RUANGAN.JENIS_KUNJUNGAN,
			refs = me.getReferences();
			rec = refs.kunjungan.getRecord();	
		if(rec) {
			var xpriv = me.getView().app.xpriv,
				notify = xpriv ? false : true;
			if(xpriv) {
				notify = !xpriv('110802', false);
			}
			var xpriv = me.getView().app.xpriv,
				notify = xpriv ? false : true;
			if(xpriv) {
				notify = !xpriv('110802', false);
			}
			if(notify) {
				me.getView().notifyMessage("Anda tidak memiliki Hak Akses untuk melakukan Pembatalan kunjungan!");
				return;
			}

			rec.showError = true;
			rec.animateTarget = t;
			rec.scope = me;
			Ext.Msg.show({
				title : "Pembatalan Kunjungan",
				message : "Anda yakin ingin membatalkan kunjungan ini ?",
				buttons : Ext.Msg.YESNO,
				icon : Ext.Msg.QUESTION,
				animateTarget : t,
				fn : function (btn) {
					if (btn === "yes") {
						view.openDialog('', true, 500, 100, {
							xtype: 'pembatalan-terima-form',
							title: 'Pembatalan Kunjungan Pendaftaran',
							ui : 'danger-red',
							showCloseButton : true
						}, function(event, win) {
							var obj = win.down('pembatalan-terima-form');
							obj.on('save', function(recs, opr, success) {
								var pembatalan = Ext.create("data.model.PembatalanKunjungan", {
									KUNJUNGAN : rec.get('NOMOR'),
									JENIS : 1,
									TANGGAL : view.getSysdate(),
									STATUS: 1
								});
								pembatalan.animateTarget = t;
								pembatalan.scope = me;
								pembatalan.showError = true;
								pembatalan.save({
									callback: function(pembatalanRec, pembatalanOpr, pembatalanSuccess) {
										if (pembatalanSuccess) {
											rec.set("STATUS", 0);
											rec.save({
												callback : function (rec, opr, success) {
													if (success) {
														me.getView().notifyMessage("Pembatalan kunjungan berhasil", "danger-blue");
														me.onReload();
													} else {
														rec.set("STATUS", 1);
														me.getView().notifyMessage("Gagal membatalkan kunjungan", "danger-red");
													}
													win.close();
												}
											});
										} else {
											me.getView().notifyMessage("Gagal menyimpan data pembatalan", "danger-red");
											win.close();
										}
									}
								});
							});
							obj.createRecord({
								NOMOR: rec.get('NOMOR'),
								JENIS_BATAL: 2,
								TANGGAL: view.getSysdate()
							});
							 
						});
						/**/
					}else {
						rec.set("STATUS", 1)
					}
				}
			});
		}
	},
	onBatalFinal: function(t) {
		var me = this,
			view = me.getView(),
			vm = me.getViewModel(),
			kjgn = vm.get('kunjungan'),
			ref = kjgn.get('REFERENSI'),
			pac = ref.PACS,
			paspul = ref.PASIEN_KELUAR,
			idRadiologi = ref.RUANGAN.ID,
			jnsKunjungan = ref.RUANGAN.JENIS_KUNJUNGAN,
			notify = true;
			//console.log(kjgn);
		if(view.app) {
			notify = !view.app.xpriv('110804');
		}
		if(notify) {
			view.notifyMessage("Anda tidak memiliki Hak Akses untuk melakukan Pembatalan final kunjungan!");
			return;
		}
		
			
			if(pac){
					if(idRadiologi == '105100101' && jnsKunjungan == 5 ){
						var	 modpacs = Ext.create("data.model.OrderRis",pac);				
						modpacs.set('FLAG',0);
						modpacs.save({
							callback: function(rec, opt, success) {
								if(success) {
									Ext.toast({
										html : "Order Pacs Dibatalkan",
										headerPosition : "left",
										style : "padding: 7px 0px 0px 4px",
										border : false,
										header : {
											width : 13
										},
										ui : "danger-blue",
										align : "t"
									});
								}
							}
						}); 
					} 
			}
							
			if(paspul){
				var pulang = Ext.create("data.model.PasienPulang",paspul);
				pulang.set("STATUS",0);
				pulang.save({
							callback: function(rec, opt, success) {
								if(success) {
									Ext.toast({
										html : "Pasien Pulang Dibatalkan",
										headerPosition : "left",
										style : "padding: 7px 0px 0px 4px",
										border : false,
										header : {
											width : 13
										},
										ui : "danger-blue",
										align : "t"
									});
								}
							}
						}); 
			}	
	
							
						
	
		
		//}else{
			Ext.Msg.show({
				title : "Pembatalan Final",
				message : "Anda yakin ingin membatalkan final kunjungan?",
				buttons : Ext.Msg.YESNO,
				icon : Ext.Msg.QUESTION,
				animateTarget : t,
				fn : function (btn) {
					if (btn === "yes") {
						view.openDialog('', true, 100, 50, {
							xtype: 'pembatalan-kunjungan-form',
							title: 'Pembatalan Final Kunjungan',
							ui : 'danger-red',
							showCloseButton : true
						}, function(event, win) {
							var obj = win.down('pembatalan-kunjungan-form');
							obj.on('save', function(rec, opr, success) {
								me.onReload();
								win.close();
							});
							obj.createRecord({
								KUNJUNGAN: kjgn.get('NOMOR'),
								JENIS: 1,
								TANGGAL: view.getSysdate()
							});
							 
							if(pac){
									if(idRadiologi == '105100101' && jnsKunjungan == 5 ){
										var	 modpacs = Ext.create("data.model.OrderRis",pac);				
										modpacs.set('FLAG',0);
										modpacs.save({
											callback: function(rec, opt, success) {
												if(success) {
													Ext.toast({
														html : "Order Pacs Dibatalkan",
														headerPosition : "left",
														style : "padding: 7px 0px 0px 4px",
														border : false,
														header : {
															width : 13
														},
														ui : "danger-blue",
														align : "t"
													});
												}
											}
										}); 
									} 
							}
							
							if(paspul){
								var pulang = Ext.create("data.model.PasienPulang",paspul);
								pulang.set("STATUS",0);
								pulang.save({
											callback: function(rec, opt, success) {
												if(success) {
													Ext.toast({
														html : "Pasien Pulang Dibatalkan",
														headerPosition : "left",
														style : "padding: 7px 0px 0px 4px",
														border : false,
														header : {
															width : 13
														},
														ui : "danger-blue",
														align : "t"
													});
												}
											}
										}); 
							}	
	
							
						});
	
					}
				}
			});
		//}	
		
	},
	
	onReopen: function(rec) {
		this.getView().fireEvent('reopen', rec);
	},
	onClose : function () {
		this.getView().fireEvent("close")
	},
	onTabChange: function(tabPanel, newCard) {
		var vm = this.getViewModel();
		if(newCard.load) newCard.load(vm.get("kunjungan"));
	}
});
