<?php
namespace Pendaftaran\V1\Rest\Kunjungan;

use DBService\pendaftaran\Pendaftaran;
use Zend\Db\Sql\Expression;
use DBService\DatabaseService;
use DBService\Service;
use DBService\System;
use Zend\Db\Sql\Select;
use Zend\Db\Sql\TableIdentifier;
use DBService\generator\Generator;
use General\V1\Rest\Ruangan\RuanganService;
use General\V1\Rest\Referensi\ReferensiService;
use Pendaftaran\V1\Rest\Pendaftaran\PendaftaranService;
use General\V1\Rest\RuangKamarTidur\RuangKamarTidurService;
use Layanan\V1\Rest\PasienPulang\PasienPulangService;
use Pembatalan\V1\Rest\PembatalanKunjungan\PembatalanKunjunganService;
use Layanan\V1\Rest\OrderLab\OrderLabService;
use Layanan\V1\Rest\OrderRad\OrderRadService;
use Layanan\V1\Rest\OrderProsedur\OrderProsedurService;
use Pacs\V1\Rest\OrderRis\OrderRisService;
use Layanan\V1\Rest\OrderResep\OrderResepService;

class KunjunganService extends Service
{
	private $ruangan;
	private $referensi;
	private $pendaftaran;
	private $ruangKamarTidur;
	private $konsul;
	private $lab;
	private $rad;
	private $farmasi;
	private $pulang;
	private $pembatalan;
	private $orderlab;
	private $orderrad;
	private $orderprosedur;
	private $mutasi;
	private $resep;
	private $cekdeposit;
	private $pacs;
	private $orderfarmasi;
	
	protected $references = array(
		'Ruangan' => true,
		'Referensi' => true,
		'Pendaftaran' => true,
		'RuangKamarTidur' => true,
		'PasienPulang' => true,
		'Pembatalan' => true,
		'Perujuk' => true,
		'Cekdeposit' => false,
		'Pacs' => true
	);
	
    public function __construct($includeReferences = true, $references = array()) {
        $this->table = DatabaseService::get('SIMpel')->get(new TableIdentifier("kunjungan", "pendaftaran"));
		$this->entity = new KunjunganEntity();
		$this->setReferences($references);
		
		$this->includeReferences = $includeReferences;
				
		if($includeReferences) {
			if($this->references['Ruangan']) $this->ruangan = new RuanganService();
			if($this->references['Referensi']) $this->referensi = new ReferensiService();
			if($this->references['Pendaftaran']) $this->pendaftaran = new PendaftaranService(true,
			array(
					'Pasien' => true,
					'PenanggungJawabPasien' => true,
					'TujuanPasien' => true,
					'SuratRujukanPasien' => true,
					'Penjamin' => true,
					'DiagnosaMasuk' => true,
					'Referensi' => true
				));
			if($this->references['RuangKamarTidur']) $this->ruangKamarTidur = new RuangKamarTidurService();
			if($this->references['PasienPulang']) $this->pulang = new PasienPulangService();
			if($this->references['Pembatalan']) $this->pembatalan = new PembatalanKunjunganService(false);			
			if($this->references['Perujuk']) {
				$this->orderlab = new OrderLabService(true, array(
					'Ruangan' => false,
					'Referensi' => false,
					'Dokter' => true,
					'OrderDetil' => false,
					'Kunjungan' => false
				));
				
				$this->orderrad = new OrderRadService(true, array(
					'Ruangan' => false,
					'Referensi' => false,
					'Dokter' => false,
					'OrderDetil' => false,
					'Kunjungan' => false
				));

				$this->orderfarmasi = new OrderResepService(true, array(
					'Ruangan' => true,
					'Referensi' => false,
					'Dokter' => false,
					'OrderDetil' => false,
					'Kunjungan' => false
				));
			}
			if($this->references['Pacs']) $this->pacs = new OrderRisService();
		}
    }
	
		
	public function masihAdaDokterKosong($id) {
		$adapter = Pendaftaran::getAdapter();
		$conn = $adapter->getDriver()->getConnection();
		$results = $conn->execute('CALL pendaftaran.MasihAdaDokterKosong('.$id.')');
		$stmt2 = $results->getResource();
		$resultset = $stmt2->fetchAll(\PDO::FETCH_OBJ);
		$found = false;
		foreach($resultset as $data) {
			$found .= $data->TINDAKAN."<br/>";
		}
		unset($stmt2);
		unset($resultset);
		return $found;
	}
	
	public function adaStokObatKosong($id) {
		$adapter = Pendaftaran::getAdapter();
		$conn = $adapter->getDriver()->getConnection();
		$results = $conn->execute('CALL pendaftaran.adaStokObatKosong('.$id.')');
		$stmt2 = $results->getResource();
		$resultset = $stmt2->fetchAll(\PDO::FETCH_OBJ);
		$found = false;
		foreach($resultset as $data) {
			$found .= $data->NAMA."<br/>";
		}
		unset($stmt2);
		unset($resultset);
		return $found;
	}
	
	public function cekDepositPasienUmum($id) {
		$adapter = $this->table->getAdapter();
		$conn = $adapter->getDriver()->getConnection();
		$results = $conn->execute('CALL pembayaran.cekDepositPasienUmum('.$id.')');
		$stmt2 = $results->getResource();
		$resultset = $stmt2->fetchAll(\PDO::FETCH_OBJ);
		$found = false;
		foreach($resultset as $data) {
			$found .= $data->DEPOSIT."<br/>";
		}
		unset($stmt2);
		unset($resultset);
		return $found;
	}
	
	public function cekStokObat($id) {
		$adapter = $this->table->getAdapter();
		$conn = $adapter->getDriver()->getConnection();
		$results = $conn->execute('CALL layanan.CekStokObat('.$id.')');
		$stmt2 = $results->getResource();
		$resultset = $stmt2->fetchAll(\PDO::FETCH_OBJ);
		$found = false;
		foreach($resultset as $data) {
			$found .= $data->Obat."<br/>";
		}
		unset($stmt2);
		unset($resultset);
		return $found;
	}
	
	public function cekStokObatLevel2($id) {
		$adapter = $this->table->getAdapter();
		$conn = $adapter->getDriver()->getConnection();
		$results = $conn->execute('CALL layanan.CekStokObatLevel2('.$id.')');
		$stmt2 = $results->getResource();
		$resultset = $stmt2->fetchAll(\PDO::FETCH_OBJ);
		$found = false;
		foreach($resultset as $data) {
			$found .= $data->Obat."<br/>";
		}
		unset($stmt2);
		unset($resultset);
		return $found;
	}
	
	public function cekStokObatLevel3($id) {
		$adapter = $this->table->getAdapter();
		$conn = $adapter->getDriver()->getConnection();
		$results = $conn->execute('CALL pendaftaran.CekStokObatLevel3('.$id.')');
		$stmt2 = $results->getResource();
		$resultset = $stmt2->fetchAll(\PDO::FETCH_OBJ);
		$found = false;
		foreach($resultset as $data) {
			$found .= $data->Obat."<br/>";
		}
		unset($stmt2);
		unset($resultset);
		return $found;
	}
	

	
	public function cekMasihAdaAmprahan($id) {
		$adapter = $this->table->getAdapter();
		$conn = $adapter->getDriver()->getConnection();
		$results = $conn->execute('CALL layanan.masihAdaAmprahanBlmFinal('.$id.')');
		$stmt2 = $results->getResource();
		$resultset = $stmt2->fetchAll(\PDO::FETCH_OBJ);
		$found = false;
		foreach($resultset as $data) {
			$found .=  date("d-m-Y H:i:s", strtotime($data->tanggal))."<br/>";
		}
		unset($stmt2);
		unset($resultset);
		return $found;
	}

	public function cekKunjunganPertamakali($id){
			$adapter = $this->table->getAdapter();
			$stmt = $adapter->query('
					SELECT * FROM pendaftaran.kunjungan pk
				WHERE pk.NOPEN = ? AND DATE(pk.MASUK) = DATE(NOW()) AND pk.REF IS NULL AND pk.STATUS=1');
				$result = $stmt->execute(array($id));
				$datap = array();
				foreach($result as $rst) {
					$datap[] = $rst; 
				}
				return $datap;
	}
        
	public function simpan($data) {
		$data = is_array($data) ? $data : (array) $data;		
		$this->entity->exchangeArray($data);
		$nomor = is_numeric($this->entity->get('NOMOR')) ? $this->entity->get('NOMOR') : 0;

		if($nomor == 0) {
			$cekresp=false;
			try {
				$ruangan = $this->entity->get('RUANGAN');
				$nomor = Generator::generateNoKunjungan($ruangan);
				$this->entity->set('NOMOR', $nomor);
				$this->entity->set('MASUK', System::getSysDate($this->table->getAdapter()));
				//$this->entity->set('MASUK', new \Zend\Db\Sql\Expression('now()'));	
				//$this->entity->set('DITERIMA_OLEH', 1);
				$this->table->insert($this->entity->getArrayCopy());
				$cekresp=true;
			} catch (\Throwable $th) {
				$cekresp=false;
			}
		
			if($cekresp==true){
				$cr = $this->cekRuanganMutasi($data['REF']);

				// Pengecekan null untuk mencegah error
				if($cr !== null && $data['STATUS']=='1') {
					if($cr['ruang_tujuan'] == '105030108') {
						$resApi = $this->calltosend('http://192.168.7.32/simrs-api/api/v1/stockart/patient', array('ref' => $data['REF'], 'eventType' => 'AdmitPatient'));
						if($resApi === false) {
							error_log("Failed to call AdmitPatient API for REF: " . $data['REF']);
						}
					}
					elseif($cr['ruang_asal'] == '105030108') {
						//$resApi = $this->calltosend('http://192.168.7.32/simrs-api/api/v1/stockart/patient', array('ref' => $data['REF'], 'nurseunit' => $cr['ruang_tujuan'], 'eventType' => 'UpdatePatient'));
						$resApi= $this->calltosend('http://192.168.7.32/simrs-api/api/v1/stockart/patient', array('ref' => $data['REF'], 'eventType' => 'DischargePatient'));
						if($resApi === false) {
							error_log("Failed to call UpdatePatient API for REF: " . $data['REF']);
						}
					}
				}

				if($nomor && $data['STATUS']=='1'){ //kirim wa RANAP ke DPJP
					$this->calltosendWA('http://192.168.7.158:8081/sendwaranap', array('nokun' => $nomor, 'key' => 'wanih'));
				}
			}

		} else {
			//var_dump("AAA");
			//var_dump($data);exit;
			$cekresp=false;
			try {
				$this->entity->set('KELUAR', new \Zend\Db\Sql\Expression('now()'));	
				$this->table->update($this->entity->getArrayCopy(), array('NOMOR' => $nomor));
				$cekresp=true;
			} catch (\Throwable $th) {
				$cekresp=false;
			}

			if($cekresp==true){
				//$getdata=$this->entity->getArrayCopy();
			
				if($this->entity->get('STATUS') == '2'){
					$jk = $this->cekRuangan($nomor);
					if($jk!=null && $jk['gedung'] == '1' && $data['KIRIMWA']==1){
						// $this->insertAntrian($jk['nopen']);
						$this->insertAntrian($data['NOMOR']);
						$this->calltosendWA('http://192.168.7.158:8081/sendwafarmasi', array('nopen' => $jk['nopen'], 'key' => 'wanih'));
					}
					if($jk['idruanganNokun'] == '105050102' || $jk['idruanganNokun'] == '105050156'){					
						 $kardek = $this->dataResepKardex($nomor);
						$jmkardek = COUNT($kardek);
						if(COUNT($kardek) > 0){
							$no=0;
							while($no < $jmkardek){
								$this->insertKardex($kardek[$no]->id_farmasi,$kardek[$no]->jumlah_obat,$kardek[$no]->jalur_pemberian,$jk['idruanganNokun']);
								$no++;
							}
						}
					}
				}
			}
		}
		
		return $this->load(array('kunjungan.NOMOR' => $nomor));
	}
	
	public function chekNotifLabPA($data) {
		$data = is_array($data) ? $data : (array) $data;
		$jk = null;
		if ($data['STATUS'] == 2) {
			$jk = $this->cekRuangan($data['NOMOR']);
	
			if ($jk != null && $jk['idruanganNokun'] == '105080101') {
				$adapter = $this->table->getAdapter();
				$conn = $adapter->getDriver()->getConnection();
	
				// Prosedur pertama
				$stmt1 = $conn->execute('CALL layanan.Notif_PA_BelumAdaNoLab(' . $data['NOMOR'] . ')');
				// $stmt1 = $conn->execute('CALL layanan.Notif_PA_BelumAdaNoLab(1050801012505200008)');
				$stmtR1 = $stmt1->getResource();
				$resultLab = $stmtR1->fetchAll(\PDO::FETCH_OBJ);
				while ($stmtR1->nextRowset()) { }
				$stmtR1 = null;
	
				$stmt2 = $conn->execute('CALL layanan.Notif_PA_BelumAdaBilling(' . $data['NOMOR'] . ')');
				$stmtR2 = $stmt2->getResource();
				$resultBil = $stmtR2->fetchAll(\PDO::FETCH_OBJ);
				while ($stmtR2->nextRowset()) { }
				$stmtR2 = null;
	
				$datapw = array();
				foreach ($resultLab as $rst) {
					if (isset($rst->WARNING)) {
						$datapw[] = $rst->WARNING;
					}
				}
				foreach ($resultBil as $rst) {
					if (isset($rst->WARNING)) {
						$datapw[] = $rst->WARNING;
					}
				}
				
				if (count($datapw) > 0) {
					return implode("<br/>", $datapw);
				}
				return null;
			}
		}
	}

	public function load($params = array(), $columns = array('*'), $orders = array()) {
		$data = parent::load($params, $columns, $orders);
		
		if($this->includeReferences) {
			foreach($data as &$entity) {
				if($this->references['Ruangan']) {
					if(is_object($this->references['Ruangan'])) {
						$references = isset($this->references['Ruangan']->REFERENSI) ? (array) $this->references['Ruangan']->REFERENSI : array();
						$this->ruangan->setReferences($references, true);
						if(isset($this->references['Ruangan']->COLUMNS)) $this->ruangan->setColumns((array) $this->references['Ruangan']->COLUMNS);
					}
					$ruangan = $this->ruangan->load(array('ID' => $entity['RUANGAN']));
					if(count($ruangan) > 0) $entity['REFERENSI']['RUANGAN'] = $ruangan[0];
				}
				
				if($this->references['Referensi']) {
					$referensi = $this->referensi->load(array('JENIS' => 31,'ID' => $entity['STATUS']));
					if(count($referensi) > 0) $entity['REFERENSI']['STATUS'] = $referensi[0];
				}
				if($this->references['Pendaftaran']) {
					if(is_object($this->references['Pendaftaran'])) {
						$references = isset($this->references['Pendaftaran']->REFERENSI) ? (array) $this->references['Pendaftaran']->REFERENSI : array();
						$this->pendaftaran->setReferences($references, true);
						if(isset($this->references['Pendaftaran']->COLUMNS)) $this->pendaftaran->setColumns((array) $this->references['Pendaftaran']->COLUMNS);
					}
					$pendaftaran = $this->pendaftaran->load(array('NOMOR' => $entity['NOPEN']));
					if(count($pendaftaran) > 0) $entity['REFERENSI']['PENDAFTARAN'] = $pendaftaran[0];
				}
				if($this->references['RuangKamarTidur'] && $entity['JENIS_KUNJUNGAN'] == 3) {
					if(is_object($this->references['RuangKamarTidur'])) {
						$references = isset($this->references['RuangKamarTidur']->REFERENSI) ? (array) $this->references['RuangKamarTidur']->REFERENSI : array();
						$this->ruangKamarTidur->setReferences($references, true);
						if(isset($this->references['RuangKamarTidur']->COLUMNS)) $this->ruangKamarTidur->setColumns((array) $this->references['RuangKamarTidur']->COLUMNS);
					}
					$ruangKamarTidur = $this->ruangKamarTidur->load(array('ID' => $entity['RUANG_KAMAR_TIDUR']));
					if(count($ruangKamarTidur) > 0) $entity['REFERENSI']['RUANG_KAMAR_TIDUR'] = $ruangKamarTidur[0];
				}
				if($this->references['PasienPulang'] && ($entity['JENIS_KUNJUNGAN'] == 2 || $entity['JENIS_KUNJUNGAN'] == 3) && $entity['STATUS'] != 0) {
					if(is_object($this->references['PasienPulang'])) {
						$references = isset($this->references['PasienPulang']->REFERENSI) ? (array) $this->references['PasienPulang']->REFERENSI : array();
						$this->pulang->setReferences($references, true);
						if(isset($this->references['PasienPulang']->COLUMNS)) $this->pulang->setColumns((array) $this->references['PasienPulang']->COLUMNS);
					}
					$pulang = $this->pulang->load(array('KUNJUNGAN' => $entity['NOMOR'] /*, 'STATUS' => 1 */));
					if(count($pulang) > 0) $entity['REFERENSI']['PASIEN_KELUAR'] = $pulang[0];
				}
				if($this->references['Pembatalan']) {
					$pembatalan = $this->pembatalan->load(array('KUNJUNGAN' => $entity['NOMOR'], 'start'=>0, 'limit'=>1), array('*'), array('TANGGAL DESC'));
					if(count($pembatalan) > 0) $entity['REFERENSI']['PEMBATALAN'] = $pembatalan[0];
				}
				/* 
				if($this->references['Pacs']) {
					$pacs = $this->pacs->load(array('NOKUN' => $entity['NOMOR']));
					if(count($pacs) > 0) $entity['REFERENSI']['PACS'] = $pacs[0];
				}
				 */
				if($this->references['Perujuk']) {
					$id = substr($entity['REF'], 0, 2);
					if($id == 12) { // Order Lab						
						$orderlab = $this->orderlab->load(array('NOMOR' => $entity['REF']));
						if(count($orderlab) > 0) $entity['REFERENSI']['PERUJUK'] = $orderlab[0];
					}
					if($id == 13) { // Order Rad						
						$orderrad = $this->orderrad->load(array('NOMOR' => $entity['REF']));
						if(count($orderrad) > 0) $entity['REFERENSI']['PERUJUK'] = $orderrad[0];
					}
					// if($id == 14) { // Order Resep						
					// 	$orderfarmasi = $this->orderfarmasi->load(array('NOMOR' => $entity['REF']));
					// 	if(count($orderfarmasi) > 0) $entity['REFERENSI']['PERUJUK'] = $orderfarmasi[0];
					// }
					// add condition for other order
				}

				if($this->references['Cekdeposit']) {
					$cekdeposit = $this->cekDepositPasienUmum($entity['NOMOR']);
					if($cekdeposit) $entity['REFERENSI']['CEKDEPOSIT'] = (float)$cekdeposit;
				}

				
			}
		}
		
		return $data;
	}
	
	protected function query($columns, $params, $isCount = false, $orders = array()) {		
		$params = is_array($params) ? $params : (array) $params;		
		return $this->table->select(function(Select $select) use ($isCount, $params, $columns, $orders) {
			if($isCount) $select->columns(array('rows' => new \Zend\Db\Sql\Expression('COUNT(1)')));
			else if(!$isCount) $select->columns($columns);
			
			if(!System::isNull($params, 'start') && !System::isNull($params, 'limit')) {	
				if(!$isCount) $select->offset((int) $params['start'])->limit((int) $params['limit']);
				unset($params['start']);
				unset($params['limit']);
			} else $select->offset(0)->limit($this->limit);
			
			if(!System::isNull($params, 'NOMOR')) {
				$nomor = $params['NOMOR'];
				$params['kunjungan.NOMOR'] = $nomor;
				unset($params['NOMOR']);
			}
			
			if(!System::isNull($params, 'RUANGAN')) {
				$ruangan = $params['RUANGAN'];
				$params['kunjungan.RUANGAN'] = $ruangan;
				unset($params['RUANGAN']);
			}
			
			if(!System::isNull($params, 'RUANG_KAMAR_TIDUR')) {
				$ruangkamar = $params['RUANG_KAMAR_TIDUR'];
				$params['kunjungan.RUANG_KAMAR_TIDUR'] = $ruangkamar;
				unset($params['RUANG_KAMAR_TIDUR']);
			}
			
			if(!System::isNull($params, 'REF')) {
				$rep = $params['REF'];
				$params['kunjungan.REF'] = $rep;
				unset($params['REF']);
			}
			
			if(!System::isNull($params, 'STATUS')) {
				$status = $params['STATUS'];
				$params['kunjungan.STATUS'] = $status;
				unset($params['STATUS']);
			}
			$select->join(array('p'=>new TableIdentifier('pendaftaran', 'pendaftaran')), 'p.NOMOR = NOPEN', array());
			
			$select->join(
				array('r' => new TableIdentifier('ruangan', 'master')),
				'r.ID = kunjungan.RUANGAN',
				array('JENIS_KUNJUNGAN')
			);
			if(isset($params['JENIS_KUNJUNGAN'])) if(!System::isNull($params, 'JENIS_KUNJUNGAN')){
				$select->where("(r.JENIS_KUNJUNGAN = ".$params['JENIS_KUNJUNGAN'].")");
				unset($params['JENIS_KUNJUNGAN']);
			}

			
			if(isset($params['TOWERC']) || isset($params['TOWERCD'])) {
				if(isset($params['TOWERC'])) if(!System::isNull($params, 'TOWERC')){
					$select->join(
						array('lr' => new TableIdentifier('order_resep', 'layanan')),
						'lr.NOMOR = REF',
						array('TANGGAL_ORDER' => 'TANGGAL')
					);
					$select->join(
						array('pkk' => new TableIdentifier('kunjungan', 'pendaftaran')),
						'pkk.NOMOR = lr.KUNJUNGAN',
						array()
					);
					$select->join(
						array('mr' => new TableIdentifier('ruangan', 'master')),
						'mr.ID = pkk.RUANGAN',
						array('RUANGASAL' => 'DESKRIPSI')
					);
					$select->join(
						array('lsc' => new TableIdentifier('scanfarmasi', 'layanan')),
						'lsc.nokun = kunjungan.NOMOR',
						array('KONFIRMASIOBAT' => 'konfirmasi_obat',
							 'DISPENOBAT' => 'dispen_mulai'	,
							 'KEMASOBAT' => 'kemas_akhir',		
						),
						Select::JOIN_LEFT
					);
					$select->join(
						array('ptp' => new TableIdentifier('tagihan_pendaftaran', 'pembayaran')),
						'ptp.PENDAFTARAN = kunjungan.NOPEN',
						array(),
						Select::JOIN_LEFT
					);
					$select->join(
						array('ppb' => new TableIdentifier('pembayaran_pra_bayar', 'pembayaran')),
						'ppb.TAGIHAN = ptp.TAGIHAN',
						array('TANGGALPRABAYAR' => 'TANGGAL'),
						Select::JOIN_LEFT
					);
					$select->join(
						array('ppt' => new TableIdentifier('pembayaran_tagihan', 'pembayaran')),
						'ppt.TAGIHAN = ptp.TAGIHAN',
						array('TANGGALBAYAR' => 'TANGGAL'),
						Select::JOIN_LEFT
					);
					$tglK1 = date('Y-m-d', strtotime("0 day", strtotime(date("Y-m-d"))));
					$tglK = date('Y-m-d', strtotime("1 day", strtotime(date("Y-m-d"))));
					$select->where("kunjungan.MASUK BETWEEN '".$tglK1." 00:00:01' AND '".$tglK." 23:59:59' AND lsc.dispen_mulai IS NULL");
					
					if(isset($params['NORMFTC'])) if(!System::isNull($params, 'NORMFTC')){
						$select->join(
							array('pp' => new TableIdentifier('pendaftaran', 'pendaftaran')),
							'pp.NOMOR = kunjungan.NOPEN',
							array()
						);
						$select->where("(pp.NORM = ".$params['NORMFTC'].")");
						unset($params['NORMFTC']);
					}
					unset($params['TOWERC']);
				}
				
				if(isset($params['TOWERCD'])) if(!System::isNull($params, 'TOWERCD')){
					$select->join(
						array('lr' => new TableIdentifier('order_resep', 'layanan')),
						'lr.NOMOR = REF',
						array('TANGGAL_ORDER' => 'TANGGAL')
					);
					$select->join(
						array('pkk' => new TableIdentifier('kunjungan', 'pendaftaran')),
						'pkk.NOMOR = lr.KUNJUNGAN',
						array()
					);
					$select->join(
						array('mr' => new TableIdentifier('ruangan', 'master')),
						'mr.ID = pkk.RUANGAN',
						array('RUANGASAL' => 'DESKRIPSI')
					);
					$select->join(
						array('lsc' => new TableIdentifier('scanfarmasi', 'layanan')),
						'lsc.nokun = kunjungan.NOMOR',
						array('KONFIRMASIOBAT' => 'konfirmasi_obat',
							 'DISPENOBAT' => 'dispen_mulai'	,
							 'KEMASOBAT' => 'kemas_akhir',		
						),
						Select::JOIN_LEFT
					);
					$select->join(
						array('ptp' => new TableIdentifier('tagihan_pendaftaran', 'pembayaran')),
						'ptp.PENDAFTARAN = kunjungan.NOPEN',
						array(),
						Select::JOIN_LEFT
					);
					$select->join(
						array('ppb' => new TableIdentifier('pembayaran_pra_bayar', 'pembayaran')),
						'ppb.TAGIHAN = ptp.TAGIHAN',
						array('TANGGALPRABAYAR' => 'TANGGAL'),
						Select::JOIN_LEFT
					);
					$select->join(
						array('ppt' => new TableIdentifier('pembayaran_tagihan', 'pembayaran')),
						'ppt.TAGIHAN = ptp.TAGIHAN',
						array('TANGGALBAYAR' => 'TANGGAL'),
						Select::JOIN_LEFT
					);
					$tglK1 = date('Y-m-d', strtotime("0 day", strtotime(date("Y-m-d"))));
					$tglK = date('Y-m-d', strtotime("1 day", strtotime(date("Y-m-d"))));
					$select->where("kunjungan.MASUK BETWEEN '".$tglK1." 00:00:01' AND '".$tglK." 23:59:59' AND lsc.dispen_mulai IS NOT NULL");
					
					if(isset($params['NORMFTC'])) if(!System::isNull($params, 'NORMFTC')){
						$select->join(
							array('pp' => new TableIdentifier('pendaftaran', 'pendaftaran')),
							'pp.NOMOR = kunjungan.NOPEN',
							array()
						);
						$select->where("(pp.NORM = ".$params['NORMFTC'].")");
						unset($params['NORMFTC']);
					}
					unset($params['TOWERCD']);
				}
			}else if($this->user && $this->privilage && !isset($params['HISTORY'])) {
				$usr = $this->user;
				$select->join(
					array('par' => new TableIdentifier('pengguna_akses_ruangan', 'aplikasi')),
					'par.RUANGAN = kunjungan.RUANGAN',
					array()
				);
				$select->where('par.STATUS = 1');
				$select->where('par.PENGGUNA = '.$usr);
			}

			
			if(!System::isNull($params, 'HISTORY')) {
				unset($params['HISTORY']);
			} 
			
			
			if(isset($params['QUERY'])) if(!System::isNull($params, 'QUERY')){
				$select->where("(kunjungan.NOMOR = ".$params['QUERY'].")");
				unset($params['QUERY']);
			}
			
			if(isset($params['ORDER'])) if(!System::isNull($params, 'ORDER')){
				$select->where("(kunjungan.STATUS >= ".$params['ORDER'].")");
				unset($params['ORDER']);
			}

			
			
			$select->where($params);
			$select->order($orders);
		})->toArray();
	}
	
	public function cekRuangan($nokun){
		$adapter = $this->table->getAdapter();
		$stmt = $adapter->query("
			SELECT mr.JENIS_KUNJUNGAN, pp.NOMOR nopen, mr.GEDUNG ,mr.ID IDRUANGANTUJUAN, rr.ID IDRUANGANNOKUN
				FROM pendaftaran.tujuan_pasien pt
				LEFT JOIN master.ruangan mr ON pt.RUANGAN = mr.ID
				LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR=pt.NOPEN
				LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN=pp.NOMOR
				LEFT JOIN master.ruangan rr ON pk.RUANGAN = rr.ID
				WHERE pk.NOMOR='$nokun'");
			$result = $stmt->execute();
			$datap = array();
			$resultSet = $result->current();
			unset($stmt);
			unset($result);
			if ($resultSet) {

				return array('jenis_kunjungan' =>$resultSet['JENIS_KUNJUNGAN'],'nopen' =>$resultSet['nopen'], 'gedung' => $resultSet['GEDUNG'],'idruanganTujuan' => $resultSet['IDRUANGANTUJUAN'], 'idruanganNokun' => $resultSet['IDRUANGANNOKUN']);

				
			}else{
				return null;
			}
				
	}

	public function insertAntrian($nokun){
		try {
			$adapter = $this->table->getAdapter();
			// $stmt = $adapter->query('CALL layanan.CreateAntrianV3_penunjang('.$nopen.')');
			$querystr="INSERT INTO antrianv2.antrian (nomr,id_jenis_antrian,ref,nomor,nomordisplay,posisi)
				SELECT duk.NORM nomr,if(tok.penjamin=1,6,7) idjenis,duk.noorder
				,(COALESCE(MAX(a.nomor),0)+1) nomor
				,tok.nomortoken notoken, duk.posisinya
				FROM
				(
				SELECT
				pp.NOMOR nopen,pp.NORM,orlab.NOMOR noorder,orlab.TUJUAN,arp.POSISI posisinya
				, 2 jenis
				FROM pendaftaran.kunjungan pk
				LEFT JOIN layanan.order_lab orlab ON orlab.KUNJUNGAN = pk.NOMOR AND orlab.STATUS>0
				LEFT JOIN antrianv2.m_ruangan_posisi arp ON arp.RUANGAN=pk.RUANGAN AND arp.JENIS=2
				LEFT JOIN pendaftaran.pendaftaran pp ON pk.NOPEN=pp.NOMOR
				LEFT JOIN pendaftaran.kunjungan pkref ON pkref.REF=orlab.NOMOR
				WHERE 1
				AND pkref.NOMOR='$nokun'
				AND orlab.NOMOR IS NOT NULL
				GROUP BY pp.NOMOR,orlab.NOMOR
				UNION
				SELECT pp.NOMOR nopen,pp.NORM,lor.NOMOR noorder,lor.TUJUAN,arp.POSISI posisinya,pk.NOMOR
				,3 jenis
				FROM pendaftaran.kunjungan pk
				LEFT JOIN layanan.order_resep lor ON lor.KUNJUNGAN = pk.NOMOR AND lor.STATUS>0
				LEFT JOIN antrianv2.m_ruangan_posisi arp ON arp.RUANGAN=pk.RUANGAN AND arp.JENIS=3
				LEFT JOIN pendaftaran.pendaftaran pp ON pk.NOPEN=pp.NOMOR
				LEFT JOIN pendaftaran.kunjungan pkref ON pkref.REF=lor.NOMOR
				WHERE 1
				AND pkref.NOMOR='$nokun'
				AND lor.NOMOR IS NOT NULL
				GROUP BY pp.NOMOR,lor.NOMOR
				) duk
				LEFT JOIN antrianv2.token tok ON tok.nopen=duk.nopen
				LEFT JOIN antrianv2.antrian a ON a.posisi IS NOT NULL AND a.posisi=duk.posisinya AND a.created_at BETWEEN
				CURDATE() AND (CURDATE() + INTERVAL 1 DAY - INTERVAL 1 SECOND)
				LEFT JOIN antrianv2.antrian cek ON cek.nomr=duk.NORM AND cek.posisi=duk.posisinya AND cek.ref=duk.noorder AND cek.nomordisplay=tok.nomortoken AND cek.status>0
				AND cek.created_at BETWEEN CURDATE() AND (CURDATE() + INTERVAL 1 DAY - INTERVAL 1 SECOND)
				WHERE 1
				AND tok.id IS NOT NULL
				AND cek.id IS NULL
				GROUP BY duk.noorder";
			$stmt = $adapter->query($querystr);
			$result = $stmt->execute();
			
		} catch (\Throwable $th) {
			//throw $th;
		}
				
	}
	
	public function calltosendWA($url, $data)
	{
		try {
			$ch = curl_init($url);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_POST, true);
			curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
			curl_setopt($ch, CURLOPT_HTTPHEADER, array(
				'Content-Type: application/x-www-form-urlencoded'
			));
			$response = curl_exec($ch);
			curl_close($ch);
			return $response;
		} catch (\Throwable $th) {
			
		}
	}

	public function cekRuanganMutasi($nomutasi){
		$adapter = $this->table->getAdapter();
		$stmt = $adapter->query("
		SELECT pmut.KUNJUNGAN nokun, mr.JENIS_KUNJUNGAN, pmut.TUJUAN ruang_tujuan,  pk.RUANGAN ruang_asal
				FROM pendaftaran.mutasi pmut
				LEFT JOIN master.ruangan mr ON mr.ID=pmut.TUJUAN
				LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR=pmut.KUNJUNGAN
				WHERE pmut.NOMOR='$nomutasi'");
			$result = $stmt->execute();
			$datap = array();
			$resultSet = $result->current();
			unset($stmt);
			unset($result);
			if ($resultSet) {
				return array('nokun' =>$resultSet['nokun'],'ruang_asal' =>$resultSet['ruang_asal'], 'ruang_tujuan' => $resultSet['ruang_tujuan']);
			}else{
				return null;
			}
				
	}

public function calltosend($url, $data, $token="")
{
	try {
		$ch = curl_init($url);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));

		$headers = array('Content-Type: application/x-www-form-urlencoded');
		if (!empty($token)) {
			$headers[] = 'Authorization: Bearer ' . $token;
		}
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

		$response = curl_exec($ch);
		$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
		curl_close($ch);

		// Log response untuk debugging
		error_log("API Call to $url - HTTP Code: $httpCode - Response: " . $response);

		return $response;

	} catch (\Throwable $th) {
		error_log("Error in calltosend: " . $th->getMessage());
		return false;
	}
}

	public function dataResepKardex($nomor) {
		$adapter = $this->table->getAdapter();
		$conn = $adapter->getDriver()->getConnection();
		$results = $conn->execute('CALL layanan.dataResepKardex('.$nomor.')');
		$stmt2 = $results->getResource();
		$resultset = $stmt2->fetchAll(\PDO::FETCH_OBJ);
		$found = array();
		foreach($resultset as $data) {
			$found[] = $data;
		}
		unset($stmt2);
		unset($resultset);
		return $found;
		
		//unset($results);
	}

	public function insertKardex($idfarmasi,$PJUMLAHOBAT,$PJALURPEMBERIAN,$PRUANGAN) {
		try {
			$adapter = $this->table->getAdapter();
			$conn = $adapter->getDriver()->getConnection();
			$results = $conn->execute('CALL layanan.insertKardex('.$idfarmasi.','.$PJUMLAHOBAT.','.$PJALURPEMBERIAN.','.$PRUANGAN.')');
		} catch (\Throwable $th) {
			//throw $th;
		}
		
	}
	
}