<?php
return array(
    'router' => array(
        'routes' => array(
            'layanan.rest.tindakan-medis' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/tindakanmedis[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\TindakanMedis\\Controller',
                    ),
                ),
            ),
            'layanan.rest.petugas-tindakan-medis' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/petugastindakanmedis[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\PetugasTindakanMedis\\Controller',
                    ),
                ),
            ),
            'layanan.rest.order-rad' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/orderrad[/:nomor]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\OrderRad\\Controller',
                    ),
                ),
            ),
            'layanan.rest.order-detil-rad' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/orderdetilrad[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\OrderDetilRad\\Controller',
                    ),
                ),
            ),
            'layanan.rest.order-lab' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/orderlab[/:nomor]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\OrderLab\\Controller',
                    ),
                ),
            ),
            'layanan.rest.order-detil-lab' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/orderdetillab[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\OrderDetilLab\\Controller',
                    ),
                ),
            ),
            'layanan.rest.hasil-rad' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/hasilrad[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\HasilRad\\Controller',
                    ),
                ),
            ),
            'layanan.rest.order-resep' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/orderresep[/:nomor]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\OrderResep\\Controller',
                    ),
                ),
            ),
            'layanan.rest.order-detil-resep' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/orderdetilresep[/:order_id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\OrderDetilResep\\Controller',
                    ),
                ),
            ),
            'layanan.rest.hasil-lab' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/hasillab[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\HasilLab\\Controller',
                    ),
                ),
            ),
            'layanan.rest.farmasi' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/farmasi[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\Farmasi\\Controller',
                    ),
                ),
            ),
            'layanan.rest.o2' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/o2[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\O2\\Controller',
                    ),
                ),
            ),
            'layanan.rest.pasien-pulang' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/pasienpulang[/:kunjungan]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\PasienPulang\\Controller',
                    ),
                ),
            ),
            'layanan.rest.pasien-meninggal' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/pasienmeninggal[/:kunjungan]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\PasienMeninggal\\Controller',
                    ),
                ),
            ),
            'layanan.rest.catatan-hasil-lab' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/catatanhasillab[/:kunjungan]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\CatatanHasilLab\\Controller',
                    ),
                ),
            ),
            'layanan.rest.retur-farmasi' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/returfarmasi[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\ReturFarmasi\\Controller',
                    ),
                ),
            ),
            'layanan.rest.hasil-pa' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/hasilpa[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\HasilPa\\Controller',
                    ),
                ),
            ),
            'layanan.rest.order-radioterapi' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/orderradioterapi[/:nomor]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\OrderRadioterapi\\Controller',
                    ),
                ),
            ),
            'layanan.rest.order-detil-radioterapi' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/orderdetilradioterapi[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\OrderDetilRadioterapi\\Controller',
                    ),
                ),
            ),
            'layanan.rest.hasil-pa-sitologi' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/hasilpasitologi[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\HasilPaSitologi\\Controller',
                    ),
                ),
            ),
            'layanan.rest.teknisi-lab' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/teknisi-lab[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\TeknisiLab\\Controller',
                    ),
                ),
            ),
            'layanan.rest.mamografi' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/mamografi[/:norm]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\Mamografi\\Controller',
                    ),
                ),
            ),
            'layanan.rest.amprahan' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/amprahan[/:nomor]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\Amprahan\\Controller',
                    ),
                ),
            ),
            'layanan.rest.amprahan-detil' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/amprahandetil[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\AmprahanDetil\\Controller',
                    ),
                ),
            ),
            'layanan.rest.order-kemo' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/orderkemo[/:order_id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\OrderKemo\\Controller',
                    ),
                ),
            ),
            'layanan.rest.hasil-pa-imuno-histokimia' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/hasilpaimunohistokimia[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\HasilPaImunoHistokimia\\Controller',
                    ),
                ),
            ),
            'layanan.rest.order-prosedur' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/orderprosedur[/:nomor]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\OrderProsedur\\Controller',
                    ),
                ),
            ),
            'layanan.rest.order-detil-prosedur' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/orderdetilprosedur[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\OrderDetilProsedur\\Controller',
                    ),
                ),
            ),
            'layanan.rest.scor-histologi-pa' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/scorhistologipa[/:no]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\ScorHistologiPa\\Controller',
                    ),
                ),
            ),
            'layanan.rest.scor-sitologi-pa' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/scorsitologipa[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\ScorSitologiPa\\Controller',
                    ),
                ),
            ),
            'layanan.rest.scor-imuno-pa' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/scorimunopa[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\ScorImunoPa\\Controller',
                    ),
                ),
            ),
            'layanan.rest.hasil-patologi-molekuler' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/hasilpatologimolekuler[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\HasilPatologiMolekuler\\Controller',
                    ),
                ),
            ),
            'layanan.rest.scor-pa-patmol' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/scorpapatmol[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\ScorPaPatmol\\Controller',
                    ),
                ),
            ),
            'layanan.rest.poin-tindakan' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/pointindakan[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\PoinTindakan\\Controller',
                    ),
                ),
            ),
            'layanan.rest.kardek-rawat' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/kardekrawat[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\KardekRawat\\Controller',
                    ),
                ),
            ),
            'layanan.rest.scor-level-histologi' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/scorlevelhistologi[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\ScorLevelHistologi\\Controller',
                    ),
                ),
            ),
            'layanan.rest.scor-level-sitologi' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/scorlevelsitologi[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\ScorLevelSitologi\\Controller',
                    ),
                ),
            ),
            'layanan.rest.scor-level-imuno' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/scorlevelimuno[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\ScorLevelImuno\\Controller',
                    ),
                ),
            ),
            'layanan.rest.hasil-pa-jenazah' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/hasilpajenazah[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\HasilPaJenazah\\Controller',
                    ),
                ),
            ),
            'layanan.rest.hasil-rad-revaluasi' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/hasilradrevaluasi[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\HasilRadRevaluasi\\Controller',
                    ),
                ),
            ),
            'layanan.rest.hasil-rad-pra-evaluasi' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/hasilradpraevaluasi[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\HasilRadPraEvaluasi\\Controller',
                    ),
                ),
            ),
            'layanan.rest.farmasi-udd' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/farmasiudd[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\FarmasiUdd\\Controller',
                    ),
                ),
            ),
            'layanan.rest.transaksi-udd' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/transaksiudd[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\TransaksiUdd\\Controller',
                    ),
                ),
            ),
            'layanan.rest.retur-udd' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/returudd[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\ReturUdd\\Controller',
                    ),
                ),
            ),
            'layanan.rest.pemberian-cairan-intravena' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/pemberiancairanintravena[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\PemberianCairanIntravena\\Controller',
                    ),
                ),
            ),
            'layanan.rest.resume-medis' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/resumemedis[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\ResumeMedis\\Controller',
                    ),
                ),
            ),
            'layanan.rest.metastasis' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/metastasis[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\Metastasis\\Controller',
                    ),
                ),
            ),
            'layanan.rest.perluasan-tumor' => array(
                'type' => 'Segment',
                'options' => array(
                    'route' => '/layanan/perluasantumor[/:id]',
                    'defaults' => array(
                        'controller' => 'Layanan\\V1\\Rest\\PerluasanTumor\\Controller',
                    ),
                ),
            ),
        ),
    ),
    'zf-versioning' => array(
        'uri' => array(
            0 => 'layanan.rest.tindakan-medis',
            1 => 'layanan.rest.petugas-tindakan-medis',
            2 => 'layanan.rest.order-rad',
            3 => 'layanan.rest.order-detil-rad',
            4 => 'layanan.rest.order-lab',
            5 => 'layanan.rest.order-detil-lab',
            6 => 'layanan.rest.order-detil-lab',
            7 => 'layanan.rest.hasil-rad',
            8 => 'layanan.rest.order-resep',
            9 => 'layanan.rest.order-detil-resep',
            10 => 'layanan.rest.hasil-lab',
            11 => 'layanan.rest.farmasi',
            12 => 'layanan.rest.o2',
            13 => 'layanan.rest.pasien-pulang',
            14 => 'layanan.rest.pasien-meninggal',
            15 => 'layanan.rest.catatan-hasil-lab',
            16 => 'layanan.rest.retur-farmasi',
            17 => 'layanan.rest.hasil-pa',
            18 => 'layanan.rest.hasil-pa',
            19 => 'layanan.rest.order-radioterapi',
            20 => 'layanan.rest.order-detil-radioterapi',
            21 => 'layanan.rest.hasil-pa-sitologi',
            22 => 'layanan.rest.teknisi-lab',
            23 => 'layanan.rest.mamografi',
            24 => 'layanan.rest.amprahan',
            25 => 'layanan.rest.amprahan-detil',
            26 => 'layanan.rest.order-kemo',
            27 => 'layanan.rest.hasil-pa-imuno-histokimia',
            29 => 'layanan.rest.order-prosedur',
            30 => 'layanan.rest.order-detil-prosedur',
            32 => 'layanan.rest.scor-histologi-pa',
            33 => 'layanan.rest.scor-sitologi-pa',
            34 => 'layanan.rest.scor-imuno-pa',
            35 => 'layanan.rest.hasil-patologi-molekuler',
            36 => 'layanan.rest.scor-pa-patmol',
            37 => 'layanan.rest.poin-tindakan',
            38 => 'layanan.rest.kardek-rawat',
            39 => 'layanan.rest.scor-level-histologi',
            40 => 'layanan.rest.scor-level-sitologi',
            41 => 'layanan.rest.scor-level-imuno',
            42 => 'layanan.rest.hasil-pa-jenazah',
            43 => 'layanan.rest.hasil-rad-revaluasi',
            44 => 'layanan.rest.hasil-rad-pra-evaluasi',
            45 => 'layanan.rest.farmasi-udd',
            46 => 'layanan.rest.transaksi-udd',
            47 => 'layanan.rest.retur-udd',
            48 => 'layanan.rest.pemberian-cairan-intravena',
            49 => 'layanan.rest.resume-medis',
            50 => 'layanan.rest.metastasis',
            51 => 'layanan.rest.perluasan-tumor',
        ),
    ),
    'service_manager' => array(
        'factories' => array(
            'Layanan\\V1\\Rest\\TindakanMedis\\TindakanMedisResource' => 'Layanan\\V1\\Rest\\TindakanMedis\\TindakanMedisResourceFactory',
            'Layanan\\V1\\Rest\\PetugasTindakanMedis\\PetugasTindakanMedisResource' => 'Layanan\\V1\\Rest\\PetugasTindakanMedis\\PetugasTindakanMedisResourceFactory',
            'Layanan\\V1\\Rest\\OrderRad\\OrderRadResource' => 'Layanan\\V1\\Rest\\OrderRad\\OrderRadResourceFactory',
            'Layanan\\V1\\Rest\\OrderDetilRad\\OrderDetilRadResource' => 'Layanan\\V1\\Rest\\OrderDetilRad\\OrderDetilRadResourceFactory',
            'Layanan\\V1\\Rest\\OrderLab\\OrderLabResource' => 'Layanan\\V1\\Rest\\OrderLab\\OrderLabResourceFactory',
            'Layanan\\V1\\Rest\\OrderDetilLab\\OrderDetilLabResource' => 'Layanan\\V1\\Rest\\OrderDetilLab\\OrderDetilLabResourceFactory',
            'Layanan\\V1\\Rest\\HasilRad\\HasilRadResource' => 'Layanan\\V1\\Rest\\HasilRad\\HasilRadResourceFactory',
            'Layanan\\V1\\Rest\\OrderResep\\OrderResepResource' => 'Layanan\\V1\\Rest\\OrderResep\\OrderResepResourceFactory',
            'Layanan\\V1\\Rest\\OrderDetilResep\\OrderDetilResepResource' => 'Layanan\\V1\\Rest\\OrderDetilResep\\OrderDetilResepResourceFactory',
            'Layanan\\V1\\Rest\\HasilLab\\HasilLabResource' => 'Layanan\\V1\\Rest\\HasilLab\\HasilLabResourceFactory',
            'Layanan\\V1\\Rest\\Farmasi\\FarmasiResource' => 'Layanan\\V1\\Rest\\Farmasi\\FarmasiResourceFactory',
            'Layanan\\V1\\Rest\\O2\\O2Resource' => 'Layanan\\V1\\Rest\\O2\\O2ResourceFactory',
            'Layanan\\V1\\Rest\\PasienPulang\\PasienPulangResource' => 'Layanan\\V1\\Rest\\PasienPulang\\PasienPulangResourceFactory',
            'Layanan\\V1\\Rest\\PasienMeninggal\\PasienMeninggalResource' => 'Layanan\\V1\\Rest\\PasienMeninggal\\PasienMeninggalResourceFactory',
            'Layanan\\V1\\Rest\\CatatanHasilLab\\CatatanHasilLabResource' => 'Layanan\\V1\\Rest\\CatatanHasilLab\\CatatanHasilLabResourceFactory',
            'Layanan\\V1\\Rest\\ReturFarmasi\\ReturFarmasiResource' => 'Layanan\\V1\\Rest\\ReturFarmasi\\ReturFarmasiResourceFactory',
            'Layanan\\V1\\Rest\\HasilPa\\HasilPaResource' => 'Layanan\\V1\\Rest\\HasilPa\\HasilPaResourceFactory',
            'Layanan\\V1\\Rest\\OrderRadioterapi\\OrderRadioterapiResource' => 'Layanan\\V1\\Rest\\OrderRadioterapi\\OrderRadioterapiResourceFactory',
            'Layanan\\V1\\Rest\\OrderDetilRadioterapi\\OrderDetilRadioterapiResource' => 'Layanan\\V1\\Rest\\OrderDetilRadioterapi\\OrderDetilRadioterapiResourceFactory',
            'Layanan\\V1\\Rest\\HasilPaSitologi\\HasilPaSitologiResource' => 'Layanan\\V1\\Rest\\HasilPaSitologi\\HasilPaSitologiResourceFactory',
            'Layanan\\V1\\Rest\\TeknisiLab\\TeknisiLabResource' => 'Layanan\\V1\\Rest\\TeknisiLab\\TeknisiLabResourceFactory',
            'Layanan\\V1\\Rest\\Mamografi\\MamografiResource' => 'Layanan\\V1\\Rest\\Mamografi\\MamografiResourceFactory',
            'Layanan\\V1\\Rest\\Amprahan\\AmprahanResource' => 'Layanan\\V1\\Rest\\Amprahan\\AmprahanResourceFactory',
            'Layanan\\V1\\Rest\\AmprahanDetil\\AmprahanDetilResource' => 'Layanan\\V1\\Rest\\AmprahanDetil\\AmprahanDetilResourceFactory',
            'Layanan\\V1\\Rest\\OrderKemo\\OrderKemoResource' => 'Layanan\\V1\\Rest\\OrderKemo\\OrderKemoResourceFactory',
            'Layanan\\V1\\Rest\\HasilPaImunoHistokimia\\HasilPaImunoHistokimiaResource' => 'Layanan\\V1\\Rest\\HasilPaImunoHistokimia\\HasilPaImunoHistokimiaResourceFactory',
            'Layanan\\V1\\Rest\\OrderProsedur\\OrderProsedurResource' => 'Layanan\\V1\\Rest\\OrderProsedur\\OrderProsedurResourceFactory',
            'Layanan\\V1\\Rest\\OrderDetilProsedur\\OrderDetilProsedurResource' => 'Layanan\\V1\\Rest\\OrderDetilProsedur\\OrderDetilProsedurResourceFactory',
            'Layanan\\V1\\Rest\\ScorHistologiPa\\ScorHistologiPaResource' => 'Layanan\\V1\\Rest\\ScorHistologiPa\\ScorHistologiPaResourceFactory',
            'Layanan\\V1\\Rest\\ScorSitologiPa\\ScorSitologiPaResource' => 'Layanan\\V1\\Rest\\ScorSitologiPa\\ScorSitologiPaResourceFactory',
            'Layanan\\V1\\Rest\\ScorImunoPa\\ScorImunoPaResource' => 'Layanan\\V1\\Rest\\ScorImunoPa\\ScorImunoPaResourceFactory',
            'Layanan\\V1\\Rest\\HasilPatologiMolekuler\\HasilPatologiMolekulerResource' => 'Layanan\\V1\\Rest\\HasilPatologiMolekuler\\HasilPatologiMolekulerResourceFactory',
            'Layanan\\V1\\Rest\\ScorPaPatmol\\ScorPaPatmolResource' => 'Layanan\\V1\\Rest\\ScorPaPatmol\\ScorPaPatmolResourceFactory',
            'Layanan\\V1\\Rest\\PoinTindakan\\PoinTindakanResource' => 'Layanan\\V1\\Rest\\PoinTindakan\\PoinTindakanResourceFactory',
            'Layanan\\V1\\Rest\\KardekRawat\\KardekRawatResource' => 'Layanan\\V1\\Rest\\KardekRawat\\KardekRawatResourceFactory',
            'Layanan\\V1\\Rest\\ScorLevelHistologi\\ScorLevelHistologiResource' => 'Layanan\\V1\\Rest\\ScorLevelHistologi\\ScorLevelHistologiResourceFactory',
            'Layanan\\V1\\Rest\\ScorLevelSitologi\\ScorLevelSitologiResource' => 'Layanan\\V1\\Rest\\ScorLevelSitologi\\ScorLevelSitologiResourceFactory',
            'Layanan\\V1\\Rest\\ScorLevelImuno\\ScorLevelImunoResource' => 'Layanan\\V1\\Rest\\ScorLevelImuno\\ScorLevelImunoResourceFactory',
            'Layanan\\V1\\Rest\\HasilPaJenazah\\HasilPaJenazahResource' => 'Layanan\\V1\\Rest\\HasilPaJenazah\\HasilPaJenazahResourceFactory',
            'Layanan\\V1\\Rest\\HasilRadRevaluasi\\HasilRadRevaluasiResource' => 'Layanan\\V1\\Rest\\HasilRadRevaluasi\\HasilRadRevaluasiResourceFactory',
            'Layanan\\V1\\Rest\\HasilRadPraEvaluasi\\HasilRadPraEvaluasiResource' => 'Layanan\\V1\\Rest\\HasilRadPraEvaluasi\\HasilRadPraEvaluasiResourceFactory',
            'Layanan\\V1\\Rest\\FarmasiUdd\\FarmasiUddResource' => 'Layanan\\V1\\Rest\\FarmasiUdd\\FarmasiUddResourceFactory',
            'Layanan\\V1\\Rest\\TransaksiUdd\\TransaksiUddResource' => 'Layanan\\V1\\Rest\\TransaksiUdd\\TransaksiUddResourceFactory',
            'Layanan\\V1\\Rest\\ReturUdd\\ReturUddResource' => 'Layanan\\V1\\Rest\\ReturUdd\\ReturUddResourceFactory',
            'Layanan\\V1\\Rest\\PemberianCairanIntravena\\PemberianCairanIntravenaResource' => 'Layanan\\V1\\Rest\\PemberianCairanIntravena\\PemberianCairanIntravenaResourceFactory',
            'Layanan\\V1\\Rest\\ResumeMedis\\ResumeMedisResource' => 'Layanan\\V1\\Rest\\ResumeMedis\\ResumeMedisResourceFactory',
            'Layanan\\V1\\Rest\\Metastasis\\MetastasisResource' => 'Layanan\\V1\\Rest\\Metastasis\\MetastasisResourceFactory',
            'Layanan\\V1\\Rest\\PerluasanTumor\\PerluasanTumorResource' => 'Layanan\\V1\\Rest\\PerluasanTumor\\PerluasanTumorResourceFactory',
        ),
    ),
    'zf-rest' => array(
        'Layanan\\V1\\Rest\\TindakanMedis\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\TindakanMedis\\TindakanMedisResource',
            'route_name' => 'layanan.rest.tindakan-medis',
            'route_identifier_name' => 'id',
            'collection_name' => 'tindakan_medis',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'NOPEN',
                1 => 'RUANGAN',
                2 => 'STATUS',
                3 => 'KUNJUNGAN',
                4 => 'start',
                5 => 'limit',
                6 => 'MEDIS',
                7 => 'TANGGAL',
                8 => 'TINDAKAN',
                9 => 'CEKRUANGPERAWATAN',
                10 => 'NOKUN',
                11 => 'CEKVISITESUDAHKAMAR',
                12 => 'CEKJUMLAHRAWAT',
                13 => 'CEKTOTALVISITESDHKAMAR',
                14 => 'CEKTOTALVISITERABER',
                15 => 'CEKTOTALTINDAKANVISITERABER',
                16 => 'DATAPOINVISITERABER',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\TindakanMedis\\TindakanMedisEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\TindakanMedis\\TindakanMedisCollection',
            'service_name' => 'TindakanMedis',
        ),
        'Layanan\\V1\\Rest\\PetugasTindakanMedis\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\PetugasTindakanMedis\\PetugasTindakanMedisResource',
            'route_name' => 'layanan.rest.petugas-tindakan-medis',
            'route_identifier_name' => 'id',
            'collection_name' => 'petugas_tindakan_medis',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'TINDAKAN_MEDIS',
                1 => 'STATUS',
                2 => 'start',
                3 => 'limit',
                4 => 'KUNJUNGAN',
                5 => 'TANGGAL',
                6 => 'MEDIS',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\PetugasTindakanMedis\\PetugasTindakanMedisEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\PetugasTindakanMedis\\PetugasTindakanMedisCollection',
            'service_name' => 'PetugasTindakanMedis',
        ),
        'Layanan\\V1\\Rest\\OrderRad\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\OrderRad\\OrderRadResource',
            'route_name' => 'layanan.rest.order-rad',
            'route_identifier_name' => 'nomor',
            'collection_name' => 'order_rad',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'NOMOR',
                1 => 'NOPEN',
                2 => 'ASAL',
                3 => 'TANGGAL',
                4 => 'TUJUAN',
                5 => 'STATUS',
                6 => 'KUNJUNGAN',
                7 => 'HISTORY',
                8 => 'NORM',
                9 => 'start',
                10 => 'limit',
                11 => 'COLUMNS',
                12 => 'REFERENSI',
                13 => 'CITO',
                14 => 'QUERY',
                15 => 'GETLASTORDER',
                16 => 'JENIS_KUNJUNGAN',
                17 => 'KUNJUNGAN_JALAN',
                18 => 'ORDER_HAJI',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\OrderRad\\OrderRadEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\OrderRad\\OrderRadCollection',
            'service_name' => 'OrderRad',
        ),
        'Layanan\\V1\\Rest\\OrderDetilRad\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\OrderDetilRad\\OrderDetilRadResource',
            'route_name' => 'layanan.rest.order-detil-rad',
            'route_identifier_name' => 'id',
            'collection_name' => 'order_detil_rad',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'ORDER_ID',
                1 => 'TINDAKAN',
                2 => 'DOLAST',
                3 => 'DOLASTINDAKAN',
                4 => 'IDTINDAKAN',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\OrderDetilRad\\OrderDetilRadEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\OrderDetilRad\\OrderDetilRadCollection',
            'service_name' => 'OrderDetilRad',
        ),
        'Layanan\\V1\\Rest\\OrderLab\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\OrderLab\\OrderLabResource',
            'route_name' => 'layanan.rest.order-lab',
            'route_identifier_name' => 'nomor',
            'collection_name' => 'order_lab',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'NOMOR',
                1 => 'NOPEN',
                2 => 'ASAL',
                3 => 'TUJUAN',
                4 => 'STATUS',
                5 => 'KUNJUNGAN',
                6 => 'HISTORY',
                7 => 'NORM',
                8 => 'start',
                9 => 'limit',
                10 => 'COLUMNS',
                11 => 'REFERENSI',
                12 => 'CITO',
                13 => 'TANGGAL',
                14 => 'JENIS_KUNJUNGAN',
                15 => 'KUNJUNGAN_JALAN',
                16 => 'ORDER_HAJI',
                17 => 'ORDERSWAB',
                18 => 'ORDERPA',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\OrderLab\\OrderLabEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\OrderLab\\OrderLabCollection',
            'service_name' => 'OrderLab',
        ),
        'Layanan\\V1\\Rest\\OrderDetilLab\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\OrderDetilLab\\OrderDetilLabResource',
            'route_name' => 'layanan.rest.order-detil-lab',
            'route_identifier_name' => 'id',
            'collection_name' => 'order_detil_lab',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'ORDER_ID',
                1 => 'TINDAKAN',
                2 => 'JMLORDER',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\OrderDetilLab\\OrderDetilLabEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\OrderDetilLab\\OrderDetilLabCollection',
            'service_name' => 'OrderDetilLab',
        ),
        'Layanan\\V1\\Rest\\HasilRad\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\HasilRad\\HasilRadResource',
            'route_name' => 'layanan.rest.hasil-rad',
            'route_identifier_name' => 'id',
            'collection_name' => 'hasil_rad',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'TANGGAL',
                1 => 'KLINIS',
                2 => 'TINDAKAN_MEDIS',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\HasilRad\\HasilRadEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\HasilRad\\HasilRadCollection',
            'service_name' => 'HasilRad',
        ),
        'Layanan\\V1\\Rest\\OrderResep\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\OrderResep\\OrderResepResource',
            'route_name' => 'layanan.rest.order-resep',
            'route_identifier_name' => 'nomor',
            'collection_name' => 'order_resep',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'NOMOR',
                1 => 'NOPEN',
                2 => 'ASAL',
                3 => 'TANGGAL',
                4 => 'DOKTER_DPJP',
                5 => 'STATUS',
                6 => 'limit',
                7 => 'start',
                8 => 'KUNJUNGAN',
                9 => 'HISTORY',
                10 => 'NORM',
                11 => 'COLUMNS',
                12 => 'REFERENSI',
                13 => 'RESEP_CITO',
                14 => 'QUERY',
                15 => 'TUJUAN',
                16 => 'RESEP_PASIEN_PULANG',
                17 => 'TELEMEDICINE',
                18 => 'RUANGAN',
                19 => 'ALKES',
                20 => 'ORDER_HAJI',
                21 => 'OBATARV',
                22 => 'ASALRESEP',
                23 => 'RESEP_BLUCODE',
                24 => 'PENUNJANG',
                25 => 'VERIFIKASI',
                26 => 'DOBLERESEP',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\OrderResep\\OrderResepEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\OrderResep\\OrderResepCollection',
            'service_name' => 'OrderResep',
        ),
        'Layanan\\V1\\Rest\\OrderDetilResep\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\OrderDetilResep\\OrderDetilResepResource',
            'route_name' => 'layanan.rest.order-detil-resep',
            'route_identifier_name' => 'order_id',
            'collection_name' => 'order_detil_resep',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'ORDER_ID',
                1 => 'FARMASI',
                2 => 'JUMLAH',
                3 => 'ATURAN_PAKAI',
                4 => 'limit',
                5 => 'start',
                6 => 'ORDEROBAT',
                7 => 'QUERY',
                8 => 'STATUS',
                9 => 'FORNAS',
                10 => 'PENGGUNA',
                11 => 'NORM',
                12 => 'HISTORYRESEP',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\OrderDetilResep\\OrderDetilResepEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\OrderDetilResep\\OrderDetilResepCollection',
            'service_name' => 'OrderDetilResep',
        ),
        'Layanan\\V1\\Rest\\HasilLab\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\HasilLab\\HasilLabResource',
            'route_name' => 'layanan.rest.hasil-lab',
            'route_identifier_name' => 'id',
            'collection_name' => 'hasil_lab',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'TINDAKAN_MEDIS',
                1 => 'PARAMETER_TINDAKAN',
                2 => 'TANGGAL',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\HasilLab\\HasilLabEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\HasilLab\\HasilLabCollection',
            'service_name' => 'HasilLab',
        ),
        'Layanan\\V1\\Rest\\Farmasi\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\Farmasi\\FarmasiResource',
            'route_name' => 'layanan.rest.farmasi',
            'route_identifier_name' => 'id',
            'collection_name' => 'farmasi',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
                4 => 'POST',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
                2 => 'PUT',
            ),
            'collection_query_whitelist' => array(
                0 => 'KUNJUNGAN',
                1 => 'FARMASI',
                2 => 'TANGGAL',
                3 => 'NORM',
                4 => 'STATUS',
                5 => 'limit',
                6 => 'start',
                7 => 'QUERY',
                8 => 'CEKKARDEK',
                9 => 'OBATTERAKHIR',
                10 => 'OBATEXPIRED',
                11 => 'UDD',
                12 => 'NOPENUDD',
                13 => 'INTRAVENA',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\Farmasi\\FarmasiEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\Farmasi\\FarmasiCollection',
            'service_name' => 'Farmasi',
        ),
        'Layanan\\V1\\Rest\\O2\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\O2\\O2Resource',
            'route_name' => 'layanan.rest.o2',
            'route_identifier_name' => 'id',
            'collection_name' => 'o2',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'KUNJUNGAN',
                1 => 'FLOW',
                2 => 'PASANG',
                3 => 'LEPAS',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\O2\\O2Entity',
            'collection_class' => 'Layanan\\V1\\Rest\\O2\\O2Collection',
            'service_name' => 'O2',
        ),
        'Layanan\\V1\\Rest\\PasienPulang\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\PasienPulang\\PasienPulangResource',
            'route_name' => 'layanan.rest.pasien-pulang',
            'route_identifier_name' => 'kunjungan',
            'collection_name' => 'pasien_pulang',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'KUNJUNGAN',
                1 => 'TANGGAL',
                2 => 'CARA',
                3 => 'DIAGNOSA',
                4 => 'NOPEN',
                5 => 'STATUS',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\PasienPulang\\PasienPulangEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\PasienPulang\\PasienPulangCollection',
            'service_name' => 'PasienPulang',
        ),
        'Layanan\\V1\\Rest\\PasienMeninggal\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\PasienMeninggal\\PasienMeninggalResource',
            'route_name' => 'layanan.rest.pasien-meninggal',
            'route_identifier_name' => 'kunjungan',
            'collection_name' => 'pasien_meninggal',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'KUNJUNGAN',
                1 => 'TANGGAL',
                2 => 'DIAGNOSA',
                3 => 'DOKTER',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\PasienMeninggal\\PasienMeninggalEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\PasienMeninggal\\PasienMeninggalCollection',
            'service_name' => 'PasienMeninggal',
        ),
        'Layanan\\V1\\Rest\\CatatanHasilLab\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\CatatanHasilLab\\CatatanHasilLabResource',
            'route_name' => 'layanan.rest.catatan-hasil-lab',
            'route_identifier_name' => 'kunjungan',
            'collection_name' => 'catatan_hasil_lab',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'KUNJUNGAN',
                1 => 'TANGGAL',
                2 => 'DOKTER',
                3 => 'STATUS',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\CatatanHasilLab\\CatatanHasilLabEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\CatatanHasilLab\\CatatanHasilLabCollection',
            'service_name' => 'CatatanHasilLab',
        ),
        'Layanan\\V1\\Rest\\ReturFarmasi\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\ReturFarmasi\\ReturFarmasiResource',
            'route_name' => 'layanan.rest.retur-farmasi',
            'route_identifier_name' => 'id',
            'collection_name' => 'retur_farmasi',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'OLEH',
                1 => 'ID_FARMASI',
                2 => 'TANGGAL',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\ReturFarmasi\\ReturFarmasiEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\ReturFarmasi\\ReturFarmasiCollection',
            'service_name' => 'ReturFarmasi',
        ),
        'Layanan\\V1\\Rest\\HasilPa\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\HasilPa\\HasilPaResource',
            'route_name' => 'layanan.rest.hasil-pa',
            'route_identifier_name' => 'id',
            'collection_name' => 'hasil_pa',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'ID',
                1 => 'NORM',
                2 => 'QUERYH',
                3 => 'JENIS_PEMERIKSAAN',
                4 => 'NOMOR_LAB',
                5 => 'KUNJUNGAN',
                6 => 'KATEGORY_PENCARIAN',
                7 => 'start',
                8 => 'limit',
                9 => 'DOKTER',
                10 => 'DASHBOR',
                11 => 'DETILNOMORLAB',
                12 => 'DASHBORUMUM',
                13 => 'DASHBORFINAL',
                14 => 'KATEGORY_PENCARIAN_DASHBOR',
                15 => 'PILIHH',
                16 => 'PENGGUNA',
                17 => 'KATEGORY_PENCARIAN_FINAL',
                18 => 'STATUS',
                19 => 'KATEGORY_PENCARIAN_UBAH_KLINISI',
                20 => 'CARISITO',
                21 => 'CARIIMUNO',
                22 => 'LAPOPRASI',
                23 => 'LAPPASCABEDAH',
                24 => 'LAPOPTINDAKAN',
                25 => 'LAPOPLETAKTUMOR',
                26 => 'LOKASISAMPEL',
                27 => 'CARIPATMOL',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\HasilPa\\HasilPaEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\HasilPa\\HasilPaCollection',
            'service_name' => 'HasilPa',
        ),
        'Layanan\\V1\\Rest\\OrderRadioterapi\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\OrderRadioterapi\\OrderRadioterapiResource',
            'route_name' => 'layanan.rest.order-radioterapi',
            'route_identifier_name' => 'nomor',
            'collection_name' => 'order_radioterapi',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'NOMOR',
                1 => 'NOPEN',
                2 => 'ASAL',
                3 => 'TANGGAL',
                4 => 'TUJUAN',
                5 => 'STATUS',
                6 => 'KUNJUNGAN',
                7 => 'HISTORY',
                8 => 'NORM',
                9 => 'start',
                10 => 'limit',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\OrderRadioterapi\\OrderRadioterapiEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\OrderRadioterapi\\OrderRadioterapiCollection',
            'service_name' => 'OrderRadioterapi',
        ),
        'Layanan\\V1\\Rest\\OrderDetilRadioterapi\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\OrderDetilRadioterapi\\OrderDetilRadioterapiResource',
            'route_name' => 'layanan.rest.order-detil-radioterapi',
            'route_identifier_name' => 'id',
            'collection_name' => 'order_detil_radioterapi',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'ORDER_ID',
                1 => 'TINDAKAN',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\OrderDetilRadioterapi\\OrderDetilRadioterapiEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\OrderDetilRadioterapi\\OrderDetilRadioterapiCollection',
            'service_name' => 'OrderDetilRadioterapi',
        ),
        'Layanan\\V1\\Rest\\HasilPaSitologi\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\HasilPaSitologi\\HasilPaSitologiResource',
            'route_name' => 'layanan.rest.hasil-pa-sitologi',
            'route_identifier_name' => 'id',
            'collection_name' => 'hasil_pa_sitologi',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'ID',
                1 => 'JENIS_PEMERIKSAAN',
                2 => 'QUERYS',
                3 => 'NORM',
                4 => 'NOMOR_LAB',
                5 => 'KUNJUNGAN',
                6 => 'KATEGORY_PENCARIAN',
                7 => 'start',
                8 => 'limit',
                9 => 'DETILNOMORSITO',
                10 => 'DASHBORUMUM',
                11 => 'DASHBORORDER',
                12 => 'DASHBORFINAL',
                13 => 'KATEGORY_PENCARIAN_DASHBOR',
                14 => 'PENGGUNA',
                15 => 'PILIHH',
                16 => 'KATEGORY_PENCARIAN_FINAL',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\HasilPaSitologi\\HasilPaSitologiEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\HasilPaSitologi\\HasilPaSitologiCollection',
            'service_name' => 'HasilPaSitologi',
        ),
        'Layanan\\V1\\Rest\\TeknisiLab\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\TeknisiLab\\TeknisiLabResource',
            'route_name' => 'layanan.rest.teknisi-lab',
            'route_identifier_name' => 'id',
            'collection_name' => 'teknisi_lab',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'HASIL_PA',
                1 => 'BARCODE',
                2 => 'TEKNISI',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\TeknisiLab\\TeknisiLabEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\TeknisiLab\\TeknisiLabCollection',
            'service_name' => 'TeknisiLab',
        ),
        'Layanan\\V1\\Rest\\Mamografi\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\Mamografi\\MamografiResource',
            'route_name' => 'layanan.rest.mamografi',
            'route_identifier_name' => 'norm',
            'collection_name' => 'mamografi',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'DOKTER',
                1 => 'NORM',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\Mamografi\\MamografiEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\Mamografi\\MamografiCollection',
            'service_name' => 'Mamografi',
        ),
        'Layanan\\V1\\Rest\\Amprahan\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\Amprahan\\AmprahanResource',
            'route_name' => 'layanan.rest.amprahan',
            'route_identifier_name' => 'nomor',
            'collection_name' => 'amprahan',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'KUNJUNGAN',
                1 => 'NOMOR',
                2 => 'STATUS',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\Amprahan\\AmprahanEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\Amprahan\\AmprahanCollection',
            'service_name' => 'Amprahan',
        ),
        'Layanan\\V1\\Rest\\AmprahanDetil\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\AmprahanDetil\\AmprahanDetilResource',
            'route_name' => 'layanan.rest.amprahan-detil',
            'route_identifier_name' => 'id',
            'collection_name' => 'amprahan_detil',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'AMPRAHAN_ID',
                1 => 'FARMASI',
                2 => 'STATUS',
                3 => 'start',
                4 => 'limit',
                5 => 'QUERY',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\AmprahanDetil\\AmprahanDetilEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\AmprahanDetil\\AmprahanDetilCollection',
            'service_name' => 'AmprahanDetil',
        ),
        'Layanan\\V1\\Rest\\OrderKemo\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\OrderKemo\\OrderKemoResource',
            'route_name' => 'layanan.rest.order-kemo',
            'route_identifier_name' => 'order_id',
            'collection_name' => 'order_kemo',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'KUNJUNGAN',
                1 => 'TANGGAL',
                2 => 'NORM',
                3 => 'STATUS',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\OrderKemo\\OrderKemoEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\OrderKemo\\OrderKemoCollection',
            'service_name' => 'OrderKemo',
        ),
        'Layanan\\V1\\Rest\\HasilPaImunoHistokimia\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\HasilPaImunoHistokimia\\HasilPaImunoHistokimiaResource',
            'route_name' => 'layanan.rest.hasil-pa-imuno-histokimia',
            'route_identifier_name' => 'id',
            'collection_name' => 'hasil_pa_imuno_histokimia',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'ID',
                1 => 'KUNJUNGAN',
                2 => 'NORM',
                3 => 'JENIS_HASIL',
                4 => 'NOMOR_LAB',
                5 => 'KATEGORY_PENCARIAN',
                6 => 'QUERYH',
                7 => 'start',
                8 => 'limit',
                9 => 'NOMOR_IMUNO',
                10 => 'DASHBORUMUM',
                11 => 'DASHBOR',
                12 => 'DETILNOMORIMUNO',
                13 => 'DASHBORFINAL',
                14 => 'KATEGORY_PENCARIAN_FINAL',
                15 => 'PILIHH',
                16 => 'PENGGUNA',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\HasilPaImunoHistokimia\\HasilPaImunoHistokimiaEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\HasilPaImunoHistokimia\\HasilPaImunoHistokimiaCollection',
            'service_name' => 'HasilPaImunoHistokimia',
        ),
        'Layanan\\V1\\Rest\\OrderProsedur\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\OrderProsedur\\OrderProsedurResource',
            'route_name' => 'layanan.rest.order-prosedur',
            'route_identifier_name' => 'nomor',
            'collection_name' => 'order_prosedur',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'NOMOR',
                1 => 'KUNJUNGAN',
                2 => 'NOPEN',
                3 => 'TANGGAL',
                4 => 'TUJUAN',
                5 => 'STATUS',
                6 => 'HISTORY',
                7 => 'NORM',
                8 => 'start',
                9 => 'limit',
                10 => 'COLUMNS',
                11 => 'REFERENSI',
                12 => 'CITO',
                13 => 'ASAL',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\OrderProsedur\\OrderProsedurEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\OrderProsedur\\OrderProsedurCollection',
            'service_name' => 'OrderProsedur',
        ),
        'Layanan\\V1\\Rest\\OrderDetilProsedur\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\OrderDetilProsedur\\OrderDetilProsedurResource',
            'route_name' => 'layanan.rest.order-detil-prosedur',
            'route_identifier_name' => 'id',
            'collection_name' => 'order_detil_prosedur',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'ORDER_ID',
                1 => 'TINDAKAN',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\OrderDetilProsedur\\OrderDetilProsedurEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\OrderDetilProsedur\\OrderDetilProsedurCollection',
            'service_name' => 'OrderDetilProsedur',
        ),
        'Layanan\\V1\\Rest\\ScorHistologiPa\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\ScorHistologiPa\\ScorHistologiPaResource',
            'route_name' => 'layanan.rest.scor-histologi-pa',
            'route_identifier_name' => 'no',
            'collection_name' => 'scor_histologi_pa',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'HASIL',
                1 => 'SCOR_ID',
                2 => 'STATUS',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\ScorHistologiPa\\ScorHistologiPaEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\ScorHistologiPa\\ScorHistologiPaCollection',
            'service_name' => 'ScorHistologiPa',
        ),
        'Layanan\\V1\\Rest\\ScorSitologiPa\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\ScorSitologiPa\\ScorSitologiPaResource',
            'route_name' => 'layanan.rest.scor-sitologi-pa',
            'route_identifier_name' => 'id',
            'collection_name' => 'scor_sitologi_pa',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'HASIL',
                1 => 'STATUS',
                2 => 'SCOR_ID',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\ScorSitologiPa\\ScorSitologiPaEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\ScorSitologiPa\\ScorSitologiPaCollection',
            'service_name' => 'ScorSitologiPa',
        ),
        'Layanan\\V1\\Rest\\ScorImunoPa\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\ScorImunoPa\\ScorImunoPaResource',
            'route_name' => 'layanan.rest.scor-imuno-pa',
            'route_identifier_name' => 'id',
            'collection_name' => 'scor_imuno_pa',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'HASIL',
                1 => 'SCOR_ID',
                2 => 'STATUS',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\ScorImunoPa\\ScorImunoPaEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\ScorImunoPa\\ScorImunoPaCollection',
            'service_name' => 'ScorImunoPa',
        ),
        'Layanan\\V1\\Rest\\HasilPatologiMolekuler\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\HasilPatologiMolekuler\\HasilPatologiMolekulerResource',
            'route_name' => 'layanan.rest.hasil-patologi-molekuler',
            'route_identifier_name' => 'id',
            'collection_name' => 'hasil_patologi_molekuler',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'NORM',
                1 => 'KUNJUNGAN',
                2 => 'NOMOR_LAB',
                3 => 'KATEGORY_PENCARIAN',
                4 => 'QUERYH',
                5 => 'start',
                6 => 'limit',
                7 => 'lisHistoCito',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\HasilPatologiMolekuler\\HasilPatologiMolekulerEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\HasilPatologiMolekuler\\HasilPatologiMolekulerCollection',
            'service_name' => 'HasilPatologiMolekuler',
        ),
        'Layanan\\V1\\Rest\\ScorPaPatmol\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\ScorPaPatmol\\ScorPaPatmolResource',
            'route_name' => 'layanan.rest.scor-pa-patmol',
            'route_identifier_name' => 'id',
            'collection_name' => 'scor_pa_patmol',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'KUNJUNGAN',
                1 => 'HASIL',
                2 => 'STATUS',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\ScorPaPatmol\\ScorPaPatmolEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\ScorPaPatmol\\ScorPaPatmolCollection',
            'service_name' => 'ScorPaPatmol',
        ),
        'Layanan\\V1\\Rest\\PoinTindakan\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\PoinTindakan\\PoinTindakanResource',
            'route_name' => 'layanan.rest.poin-tindakan',
            'route_identifier_name' => 'id',
            'collection_name' => 'poin_tindakan',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\PoinTindakan\\PoinTindakanEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\PoinTindakan\\PoinTindakanCollection',
            'service_name' => 'PoinTindakan',
        ),
        'Layanan\\V1\\Rest\\KardekRawat\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\KardekRawat\\KardekRawatResource',
            'route_name' => 'layanan.rest.kardek-rawat',
            'route_identifier_name' => 'id',
            'collection_name' => 'kardek_rawat',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\KardekRawat\\KardekRawatEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\KardekRawat\\KardekRawatCollection',
            'service_name' => 'KardekRawat',
        ),
        'Layanan\\V1\\Rest\\ScorLevelHistologi\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\ScorLevelHistologi\\ScorLevelHistologiResource',
            'route_name' => 'layanan.rest.scor-level-histologi',
            'route_identifier_name' => 'id',
            'collection_name' => 'scor_level_histologi',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'ID_HASIL',
                1 => 'STATUS',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\ScorLevelHistologi\\ScorLevelHistologiEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\ScorLevelHistologi\\ScorLevelHistologiCollection',
            'service_name' => 'ScorLevelHistologi',
        ),
        'Layanan\\V1\\Rest\\ScorLevelSitologi\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\ScorLevelSitologi\\ScorLevelSitologiResource',
            'route_name' => 'layanan.rest.scor-level-sitologi',
            'route_identifier_name' => 'id',
            'collection_name' => 'scor_level_sitologi',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'KUNJUNGAN',
                1 => 'ID_HASIL',
                2 => 'STATUS',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\ScorLevelSitologi\\ScorLevelSitologiEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\ScorLevelSitologi\\ScorLevelSitologiCollection',
            'service_name' => 'ScorLevelSitologi',
        ),
        'Layanan\\V1\\Rest\\ScorLevelImuno\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\ScorLevelImuno\\ScorLevelImunoResource',
            'route_name' => 'layanan.rest.scor-level-imuno',
            'route_identifier_name' => 'id',
            'collection_name' => 'scor_level_imuno',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'KUNJUNGAN',
                1 => 'STATUS',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\ScorLevelImuno\\ScorLevelImunoEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\ScorLevelImuno\\ScorLevelImunoCollection',
            'service_name' => 'ScorLevelImuno',
        ),
        'Layanan\\V1\\Rest\\HasilPaJenazah\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\HasilPaJenazah\\HasilPaJenazahResource',
            'route_name' => 'layanan.rest.hasil-pa-jenazah',
            'route_identifier_name' => 'id',
            'collection_name' => 'hasil_pa_jenazah',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'KUNJUNGAN',
                1 => 'NORM',
                2 => 'KATEGORY_PENCARIAN',
                3 => 'TGLMENINGGAL',
                4 => 'DIAGNOSAEMR',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\HasilPaJenazah\\HasilPaJenazahEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\HasilPaJenazah\\HasilPaJenazahCollection',
            'service_name' => 'HasilPaJenazah',
        ),
        'Layanan\\V1\\Rest\\HasilRadRevaluasi\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\HasilRadRevaluasi\\HasilRadRevaluasiResource',
            'route_name' => 'layanan.rest.hasil-rad-revaluasi',
            'route_identifier_name' => 'id',
            'collection_name' => 'hasil_rad_revaluasi',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\HasilRadRevaluasi\\HasilRadRevaluasiEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\HasilRadRevaluasi\\HasilRadRevaluasiCollection',
            'service_name' => 'HasilRadRevaluasi',
        ),
        'Layanan\\V1\\Rest\\HasilRadPraEvaluasi\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\HasilRadPraEvaluasi\\HasilRadPraEvaluasiResource',
            'route_name' => 'layanan.rest.hasil-rad-pra-evaluasi',
            'route_identifier_name' => 'id',
            'collection_name' => 'hasil_rad_pra_evaluasi',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'TINDAKAN_MEDIS',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\HasilRadPraEvaluasi\\HasilRadPraEvaluasiEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\HasilRadPraEvaluasi\\HasilRadPraEvaluasiCollection',
            'service_name' => 'HasilRadPraEvaluasi',
        ),
        'Layanan\\V1\\Rest\\FarmasiUdd\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\FarmasiUdd\\FarmasiUddResource',
            'route_name' => 'layanan.rest.farmasi-udd',
            'route_identifier_name' => 'id',
            'collection_name' => 'farmasi_udd',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'IDFARMASI',
                1 => 'KUNJUNGAN',
                2 => 'NOPEN',
                3 => 'QUERY',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\FarmasiUdd\\FarmasiUddEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\FarmasiUdd\\FarmasiUddCollection',
            'service_name' => 'FarmasiUdd',
        ),
        'Layanan\\V1\\Rest\\TransaksiUdd\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\TransaksiUdd\\TransaksiUddResource',
            'route_name' => 'layanan.rest.transaksi-udd',
            'route_identifier_name' => 'id',
            'collection_name' => 'transaksi_udd',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'IDFARMASI',
                1 => 'KUNJUNGAN',
                2 => 'IDTRANSAKSI',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\TransaksiUdd\\TransaksiUddEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\TransaksiUdd\\TransaksiUddCollection',
            'service_name' => 'TransaksiUdd',
        ),
        'Layanan\\V1\\Rest\\ReturUdd\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\ReturUdd\\ReturUddResource',
            'route_name' => 'layanan.rest.retur-udd',
            'route_identifier_name' => 'id',
            'collection_name' => 'retur_udd',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'ID_FARMASI',
                1 => 'IDTRANSAKSI',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\ReturUdd\\ReturUddEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\ReturUdd\\ReturUddCollection',
            'service_name' => 'ReturUdd',
        ),
        'Layanan\\V1\\Rest\\PemberianCairanIntravena\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\PemberianCairanIntravena\\PemberianCairanIntravenaResource',
            'route_name' => 'layanan.rest.pemberian-cairan-intravena',
            'route_identifier_name' => 'id',
            'collection_name' => 'pemberian_cairan_intravena',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\PemberianCairanIntravena\\PemberianCairanIntravenaEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\PemberianCairanIntravena\\PemberianCairanIntravenaCollection',
            'service_name' => 'PemberianCairanIntravena',
        ),
        'Layanan\\V1\\Rest\\ResumeMedis\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\ResumeMedis\\ResumeMedisResource',
            'route_name' => 'layanan.rest.resume-medis',
            'route_identifier_name' => 'id',
            'collection_name' => 'resume_medis',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'nopen',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\ResumeMedis\\ResumeMedisEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\ResumeMedis\\ResumeMedisCollection',
            'service_name' => 'ResumeMedis',
        ),
        'Layanan\\V1\\Rest\\Metastasis\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\Metastasis\\MetastasisResource',
            'route_name' => 'layanan.rest.metastasis',
            'route_identifier_name' => 'id',
            'collection_name' => 'metastasis',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'KUNJUNGAN',
                1 => 'TINDAKAN_MEDIS',
                2 => 'VARIABELMETA',
                3 => 'NOMR',
                4 => 'STATUS',
                5 => 'JENIS',
                6 => 'VARIABELPELTU',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\Metastasis\\MetastasisEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\Metastasis\\MetastasisCollection',
            'service_name' => 'Metastasis',
        ),
        'Layanan\\V1\\Rest\\PerluasanTumor\\Controller' => array(
            'listener' => 'Layanan\\V1\\Rest\\PerluasanTumor\\PerluasanTumorResource',
            'route_name' => 'layanan.rest.perluasan-tumor',
            'route_identifier_name' => 'id',
            'collection_name' => 'perluasan_tumor',
            'entity_http_methods' => array(
                0 => 'GET',
                1 => 'PATCH',
                2 => 'PUT',
                3 => 'DELETE',
            ),
            'collection_http_methods' => array(
                0 => 'GET',
                1 => 'POST',
            ),
            'collection_query_whitelist' => array(
                0 => 'KUNJUNGAN',
                1 => 'TINDAKAN_MEDIS',
            ),
            'page_size' => 25,
            'page_size_param' => null,
            'entity_class' => 'Layanan\\V1\\Rest\\PerluasanTumor\\PerluasanTumorEntity',
            'collection_class' => 'Layanan\\V1\\Rest\\PerluasanTumor\\PerluasanTumorCollection',
            'service_name' => 'PerluasanTumor',
        ),
    ),
    'zf-content-negotiation' => array(
        'controllers' => array(
            'Layanan\\V1\\Rest\\TindakanMedis\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\PetugasTindakanMedis\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\OrderRad\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\OrderDetilRad\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\OrderLab\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\OrderDetilLab\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\HasilRad\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\OrderResep\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\OrderDetilResep\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\HasilLab\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\Farmasi\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\O2\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\PasienPulang\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\PasienMeninggal\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\CatatanHasilLab\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\ReturFarmasi\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\HasilPa\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\OrderRadioterapi\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\OrderDetilRadioterapi\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\HasilPaSitologi\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\TeknisiLab\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\Mamografi\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\Amprahan\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\AmprahanDetil\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\OrderKemo\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\HasilPaImunoHistokimia\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\OrderProsedur\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\OrderDetilProsedur\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\ScorHistologiPa\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\ScorSitologiPa\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\ScorImunoPa\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\HasilPatologiMolekuler\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\ScorPaPatmol\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\PoinTindakan\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\KardekRawat\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\ScorLevelHistologi\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\ScorLevelSitologi\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\ScorLevelImuno\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\HasilPaJenazah\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\HasilRadRevaluasi\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\HasilRadPraEvaluasi\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\FarmasiUdd\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\TransaksiUdd\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\ReturUdd\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\PemberianCairanIntravena\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\ResumeMedis\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\Metastasis\\Controller' => 'Json',
            'Layanan\\V1\\Rest\\PerluasanTumor\\Controller' => 'Json',
        ),
        'accept_whitelist' => array(
            'Layanan\\V1\\Rest\\TindakanMedis\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\PetugasTindakanMedis\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\OrderRad\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\OrderDetilRad\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\OrderLab\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\OrderDetilLab\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\HasilRad\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\OrderResep\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\OrderDetilResep\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\HasilLab\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\Farmasi\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\O2\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\PasienPulang\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\PasienMeninggal\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\CatatanHasilLab\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\ReturFarmasi\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\HasilPa\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\OrderRadioterapi\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\OrderDetilRadioterapi\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\HasilPaSitologi\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\TeknisiLab\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\Mamografi\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\Amprahan\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\AmprahanDetil\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\OrderKemo\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\HasilPaImunoHistokimia\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\OrderProsedur\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\OrderDetilProsedur\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\ScorHistologiPa\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\ScorSitologiPa\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\ScorImunoPa\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\HasilPatologiMolekuler\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\ScorPaPatmol\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\PoinTindakan\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\KardekRawat\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\ScorLevelHistologi\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\ScorLevelSitologi\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\ScorLevelImuno\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\HasilPaJenazah\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\HasilRadRevaluasi\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\HasilRadPraEvaluasi\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\FarmasiUdd\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\TransaksiUdd\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\ReturUdd\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\PemberianCairanIntravena\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\ResumeMedis\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\Metastasis\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\PerluasanTumor\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/hal+json',
                2 => 'application/json',
            ),
        ),
        'content_type_whitelist' => array(
            'Layanan\\V1\\Rest\\TindakanMedis\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\PetugasTindakanMedis\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\OrderRad\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\OrderDetilRad\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\OrderLab\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\OrderDetilLab\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\HasilRad\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\OrderResep\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\OrderDetilResep\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\HasilLab\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\Farmasi\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\O2\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\PasienPulang\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\PasienMeninggal\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\CatatanHasilLab\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\ReturFarmasi\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\HasilPa\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\OrderRadioterapi\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\OrderDetilRadioterapi\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\HasilPaSitologi\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\TeknisiLab\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\Mamografi\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\Amprahan\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\AmprahanDetil\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\OrderKemo\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\HasilPaImunoHistokimia\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\OrderProsedur\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\OrderDetilProsedur\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\ScorHistologiPa\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\ScorSitologiPa\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\ScorImunoPa\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\HasilPatologiMolekuler\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\ScorPaPatmol\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\PoinTindakan\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\KardekRawat\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\ScorLevelHistologi\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\ScorLevelSitologi\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\ScorLevelImuno\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\HasilPaJenazah\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\HasilRadRevaluasi\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\HasilRadPraEvaluasi\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\FarmasiUdd\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\TransaksiUdd\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\ReturUdd\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\PemberianCairanIntravena\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\ResumeMedis\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\Metastasis\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
            'Layanan\\V1\\Rest\\PerluasanTumor\\Controller' => array(
                0 => 'application/vnd.layanan.v1+json',
                1 => 'application/json',
            ),
        ),
    ),
    'zf-hal' => array(
        'metadata_map' => array(
            'Layanan\\V1\\Rest\\TindakanMedis\\TindakanMedisEntity' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.tindakan-medis',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\TindakanMedis\\TindakanMedisCollection' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.tindakan-medis',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\PetugasTindakanMedis\\PetugasTindakanMedisEntity' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.petugas-tindakan-medis',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\PetugasTindakanMedis\\PetugasTindakanMedisCollection' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.petugas-tindakan-medis',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\OrderRad\\OrderRadEntity' => array(
                'entity_identifier_name' => 'NOMOR',
                'route_name' => 'layanan.rest.order-rad',
                'route_identifier_name' => 'nomor',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\OrderRad\\OrderRadCollection' => array(
                'entity_identifier_name' => 'NOMOR',
                'route_name' => 'layanan.rest.order-rad',
                'route_identifier_name' => 'nomor',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\OrderDetilRad\\OrderDetilRadEntity' => array(
                'entity_identifier_name' => 'ORDER_ID',
                'route_name' => 'layanan.rest.order-detil-rad',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\OrderDetilRad\\OrderDetilRadCollection' => array(
                'entity_identifier_name' => 'ORDER_ID',
                'route_name' => 'layanan.rest.order-detil-rad',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\OrderLab\\OrderLabEntity' => array(
                'entity_identifier_name' => 'NOMOR',
                'route_name' => 'layanan.rest.order-lab',
                'route_identifier_name' => 'nomor',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\OrderLab\\OrderLabCollection' => array(
                'entity_identifier_name' => 'NOMOR',
                'route_name' => 'layanan.rest.order-lab',
                'route_identifier_name' => 'nomor',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\OrderDetilLab\\OrderDetilLabEntity' => array(
                'entity_identifier_name' => 'ORDER_ID',
                'route_name' => 'layanan.rest.order-detil-lab',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\OrderDetilLab\\OrderDetilLabCollection' => array(
                'entity_identifier_name' => 'ORDER_ID',
                'route_name' => 'layanan.rest.order-detil-lab',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\HasilRad\\HasilRadEntity' => array(
                'entity_identifier_name' => 'TINDAKAN_MEDIS',
                'route_name' => 'layanan.rest.hasil-rad',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\HasilRad\\HasilRadCollection' => array(
                'entity_identifier_name' => 'TINDAKAN_MEDIS',
                'route_name' => 'layanan.rest.hasil-rad',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\OrderResep\\OrderResepEntity' => array(
                'entity_identifier_name' => 'NOMOR',
                'route_name' => 'layanan.rest.order-resep',
                'route_identifier_name' => 'nomor',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\OrderResep\\OrderResepCollection' => array(
                'entity_identifier_name' => 'NOMOR',
                'route_name' => 'layanan.rest.order-resep',
                'route_identifier_name' => 'nomor',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\OrderDetilResep\\OrderDetilResepEntity' => array(
                'entity_identifier_name' => 'id',
                'route_name' => 'layanan.rest.order-detil-resep',
                'route_identifier_name' => 'order_id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\OrderDetilResep\\OrderDetilResepCollection' => array(
                'entity_identifier_name' => 'id',
                'route_name' => 'layanan.rest.order-detil-resep',
                'route_identifier_name' => 'order_id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\HasilLab\\HasilLabEntity' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.hasil-lab',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\HasilLab\\HasilLabCollection' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.hasil-lab',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\Farmasi\\FarmasiEntity' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.farmasi',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\Farmasi\\FarmasiCollection' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.farmasi',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\O2\\O2Entity' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.o2',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\O2\\O2Collection' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.o2',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\PasienPulang\\PasienPulangEntity' => array(
                'entity_identifier_name' => 'KUNJUNGAN',
                'route_name' => 'layanan.rest.pasien-pulang',
                'route_identifier_name' => 'kunjungan',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\PasienPulang\\PasienPulangCollection' => array(
                'entity_identifier_name' => 'KUNJUNGAN',
                'route_name' => 'layanan.rest.pasien-pulang',
                'route_identifier_name' => 'kunjungan',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\PasienMeninggal\\PasienMeninggalEntity' => array(
                'entity_identifier_name' => 'KUNJUNGAN',
                'route_name' => 'layanan.rest.pasien-meninggal',
                'route_identifier_name' => 'kunjungan',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\PasienMeninggal\\PasienMeninggalCollection' => array(
                'entity_identifier_name' => 'KUNJUNGAN',
                'route_name' => 'layanan.rest.pasien-meninggal',
                'route_identifier_name' => 'kunjungan',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\CatatanHasilLab\\CatatanHasilLabEntity' => array(
                'entity_identifier_name' => 'KUNJUNGAN',
                'route_name' => 'layanan.rest.catatan-hasil-lab',
                'route_identifier_name' => 'kunjungan',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\CatatanHasilLab\\CatatanHasilLabCollection' => array(
                'entity_identifier_name' => 'KUNJUNGAN',
                'route_name' => 'layanan.rest.catatan-hasil-lab',
                'route_identifier_name' => 'kunjungan',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\ReturFarmasi\\ReturFarmasiEntity' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.retur-farmasi',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\ReturFarmasi\\ReturFarmasiCollection' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.retur-farmasi',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\HasilPa\\HasilPaEntity' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.hasil-pa',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\HasilPa\\HasilPaCollection' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.hasil-pa',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\OrderRadioterapi\\OrderRadioterapiEntity' => array(
                'entity_identifier_name' => 'NOMOR',
                'route_name' => 'layanan.rest.order-radioterapi',
                'route_identifier_name' => 'nomor',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\OrderRadioterapi\\OrderRadioterapiCollection' => array(
                'entity_identifier_name' => 'NOMOR',
                'route_name' => 'layanan.rest.order-radioterapi',
                'route_identifier_name' => 'nomor',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\OrderDetilRadioterapi\\OrderDetilRadioterapiEntity' => array(
                'entity_identifier_name' => 'ORDER_ID',
                'route_name' => 'layanan.rest.order-detil-radioterapi',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\OrderDetilRadioterapi\\OrderDetilRadioterapiCollection' => array(
                'entity_identifier_name' => 'ORDER_ID',
                'route_name' => 'layanan.rest.order-detil-radioterapi',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\HasilPaSitologi\\HasilPaSitologiEntity' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.hasil-pa-sitologi',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\HasilPaSitologi\\HasilPaSitologiCollection' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.hasil-pa-sitologi',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\TeknisiLab\\TeknisiLabEntity' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.teknisi-lab',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\TeknisiLab\\TeknisiLabCollection' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.teknisi-lab',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\Mamografi\\MamografiEntity' => array(
                'entity_identifier_name' => 'NORM',
                'route_name' => 'layanan.rest.mamografi',
                'route_identifier_name' => 'norm',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\Mamografi\\MamografiCollection' => array(
                'entity_identifier_name' => 'NORM',
                'route_name' => 'layanan.rest.mamografi',
                'route_identifier_name' => 'norm',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\Amprahan\\AmprahanEntity' => array(
                'entity_identifier_name' => 'NOMOR',
                'route_name' => 'layanan.rest.amprahan',
                'route_identifier_name' => 'nomor',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\Amprahan\\AmprahanCollection' => array(
                'entity_identifier_name' => 'NOMOR',
                'route_name' => 'layanan.rest.amprahan',
                'route_identifier_name' => 'nomor',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\AmprahanDetil\\AmprahanDetilEntity' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.amprahan-detil',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\AmprahanDetil\\AmprahanDetilCollection' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.amprahan-detil',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\OrderKemo\\OrderKemoEntity' => array(
                'entity_identifier_name' => 'ORDER_ID',
                'route_name' => 'layanan.rest.order-kemo',
                'route_identifier_name' => 'order_id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\OrderKemo\\OrderKemoCollection' => array(
                'entity_identifier_name' => 'ORDER_ID',
                'route_name' => 'layanan.rest.order-kemo',
                'route_identifier_name' => 'order_id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\HasilPaImunoHistokimia\\HasilPaImunoHistokimiaEntity' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.hasil-pa-imuno-histokimia',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\HasilPaImunoHistokimia\\HasilPaImunoHistokimiaCollection' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.hasil-pa-imuno-histokimia',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\OrderProsedur\\OrderProsedurEntity' => array(
                'entity_identifier_name' => 'NOMOR',
                'route_name' => 'layanan.rest.order-prosedur',
                'route_identifier_name' => 'nomor',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\OrderProsedur\\OrderProsedurCollection' => array(
                'entity_identifier_name' => 'NOMOR',
                'route_name' => 'layanan.rest.order-prosedur',
                'route_identifier_name' => 'nomor',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\OrderDetilProsedur\\OrderDetilProsedurEntity' => array(
                'entity_identifier_name' => 'ORDER_ID',
                'route_name' => 'layanan.rest.order-detil-prosedur',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\OrderDetilProsedur\\OrderDetilProsedurCollection' => array(
                'entity_identifier_name' => 'ORDER_ID',
                'route_name' => 'layanan.rest.order-detil-prosedur',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\ScorHistologiPa\\ScorHistologiPaEntity' => array(
                'entity_identifier_name' => 'NO',
                'route_name' => 'layanan.rest.scor-histologi-pa',
                'route_identifier_name' => 'no',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\ScorHistologiPa\\ScorHistologiPaCollection' => array(
                'entity_identifier_name' => 'NO',
                'route_name' => 'layanan.rest.scor-histologi-pa',
                'route_identifier_name' => 'no',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\ScorSitologiPa\\ScorSitologiPaEntity' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.scor-sitologi-pa',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\ScorSitologiPa\\ScorSitologiPaCollection' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.scor-sitologi-pa',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\ScorImunoPa\\ScorImunoPaEntity' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.scor-imuno-pa',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\ScorImunoPa\\ScorImunoPaCollection' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.scor-imuno-pa',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\HasilPatologiMolekuler\\HasilPatologiMolekulerEntity' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.hasil-patologi-molekuler',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\HasilPatologiMolekuler\\HasilPatologiMolekulerCollection' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.hasil-patologi-molekuler',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\ScorPaPatmol\\ScorPaPatmolEntity' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.scor-pa-patmol',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\ScorPaPatmol\\ScorPaPatmolCollection' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.scor-pa-patmol',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\PoinTindakan\\PoinTindakanEntity' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.poin-tindakan',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\PoinTindakan\\PoinTindakanCollection' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.poin-tindakan',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\KardekRawat\\KardekRawatEntity' => array(
                'entity_identifier_name' => 'id_farmasi',
                'route_name' => 'layanan.rest.kardek-rawat',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\KardekRawat\\KardekRawatCollection' => array(
                'entity_identifier_name' => 'id_farmasi',
                'route_name' => 'layanan.rest.kardek-rawat',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\ScorLevelHistologi\\ScorLevelHistologiEntity' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.scor-level-histologi',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\ScorLevelHistologi\\ScorLevelHistologiCollection' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.scor-level-histologi',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\ScorLevelSitologi\\ScorLevelSitologiEntity' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.scor-level-sitologi',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\ScorLevelSitologi\\ScorLevelSitologiCollection' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.scor-level-sitologi',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\ScorLevelImuno\\ScorLevelImunoEntity' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.scor-level-imuno',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\ScorLevelImuno\\ScorLevelImunoCollection' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.scor-level-imuno',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\HasilPaJenazah\\HasilPaJenazahEntity' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.hasil-pa-jenazah',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\HasilPaJenazah\\HasilPaJenazahCollection' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.hasil-pa-jenazah',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\HasilRadRevaluasi\\HasilRadRevaluasiEntity' => array(
                'entity_identifier_name' => 'id',
                'route_name' => 'layanan.rest.hasil-rad-revaluasi',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\HasilRadRevaluasi\\HasilRadRevaluasiCollection' => array(
                'entity_identifier_name' => 'id',
                'route_name' => 'layanan.rest.hasil-rad-revaluasi',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\HasilRadPraEvaluasi\\HasilRadPraEvaluasiEntity' => array(
                'entity_identifier_name' => 'id',
                'route_name' => 'layanan.rest.hasil-rad-pra-evaluasi',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\HasilRadPraEvaluasi\\HasilRadPraEvaluasiCollection' => array(
                'entity_identifier_name' => 'id',
                'route_name' => 'layanan.rest.hasil-rad-pra-evaluasi',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\FarmasiUdd\\FarmasiUddEntity' => array(
                'entity_identifier_name' => 'IDFARMASI',
                'route_name' => 'layanan.rest.farmasi-udd',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\FarmasiUdd\\FarmasiUddCollection' => array(
                'entity_identifier_name' => 'IDFARMASI',
                'route_name' => 'layanan.rest.farmasi-udd',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\TransaksiUdd\\TransaksiUddEntity' => array(
                'entity_identifier_name' => 'id',
                'route_name' => 'layanan.rest.transaksi-udd',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\TransaksiUdd\\TransaksiUddCollection' => array(
                'entity_identifier_name' => 'id',
                'route_name' => 'layanan.rest.transaksi-udd',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\ReturUdd\\ReturUddEntity' => array(
                'entity_identifier_name' => 'id',
                'route_name' => 'layanan.rest.retur-udd',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\ReturUdd\\ReturUddCollection' => array(
                'entity_identifier_name' => 'id',
                'route_name' => 'layanan.rest.retur-udd',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\PemberianCairanIntravena\\PemberianCairanIntravenaEntity' => array(
                'entity_identifier_name' => 'id',
                'route_name' => 'layanan.rest.pemberian-cairan-intravena',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\PemberianCairanIntravena\\PemberianCairanIntravenaCollection' => array(
                'entity_identifier_name' => 'id',
                'route_name' => 'layanan.rest.pemberian-cairan-intravena',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\ResumeMedis\\ResumeMedisEntity' => array(
                'entity_identifier_name' => 'id',
                'route_name' => 'layanan.rest.resume-medis',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\ResumeMedis\\ResumeMedisCollection' => array(
                'entity_identifier_name' => 'id',
                'route_name' => 'layanan.rest.resume-medis',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\Metastasis\\MetastasisEntity' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.metastasis',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\Metastasis\\MetastasisCollection' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.metastasis',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
            'Layanan\\V1\\Rest\\PerluasanTumor\\PerluasanTumorEntity' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.perluasan-tumor',
                'route_identifier_name' => 'id',
                'hydrator' => 'Zend\\Stdlib\\Hydrator\\ArraySerializable',
            ),
            'Layanan\\V1\\Rest\\PerluasanTumor\\PerluasanTumorCollection' => array(
                'entity_identifier_name' => 'ID',
                'route_name' => 'layanan.rest.perluasan-tumor',
                'route_identifier_name' => 'id',
                'is_collection' => true,
            ),
        ),
    ),
);
