Ext.define("layanan.pa.histo.entrinopa.listdata.Form", {
	extend : "com.Form",
	xtype : "listdata-entrinopa-form",
	controller : "listdata-entrinopa-form",
	viewModel : {
		stores : {
			store : {
				type : "hasilpa-store"
			},
			jenis<PERSON>ayananPa : {
				type : "jenis-layanan-pa-store"
			}
		},
		data : {
			tglmax : undefined,
			record : undefined,
			norm : undefined,
			nomor : undefined,
			nokun : undefined			
		}
	},
	bodyPadding : 10,
	defaults: {
		anchor: '100%',
		margin: '0 0 1 0',
		fieldStyle:"font-size: 18px; font-weigth:bold; background-color:#b3e6ff;"
	},
	items : [{
			xtype: 'datetimefield',
			name: 'TANGGAL_LAB',
			value : new Date(),
            submitFormat: 'Y-m-d H:i:s',
			bind : {
					maxValue: "{tglmax}"
			},
			allowBlank : false,
			reference : 'tgllab',
			emptyText: '[ Tanggal Lab ]'
		},{
				xtype: "combobox",
				name: "JENIS_PEMERIKSAAN",
				emptyText : "[ JENIS PEMERIKSAAN ]",
				store: Ext.create("Ext.data.Store", {
					fields: ["id", "name"],
					data: [
						{"id": 1, "name": "REGULER"},
						{"id": 2, "name": "VC"},
						{"id": 3, "name": "KONSUL/REVIEW"}
					]
				}),
				displayField: "name",
				valueField: "id",
				allowBlank : false,
				editable: false,
				width: 300,
				listeners : {
					specialkey : "onFocusEnter"
				}
		},{
				xtype: "combobox",
				name: "JENIS_JARINGAN",
				emptyText : "[ JENIS JARINGAN ]",
				store: Ext.create("Ext.data.Store", {
					fields: ["id", "name"],
					data: [
						{"id": 1, "name": "JARINGAN KECIL"},
						{"id": 2, "name": "JARINGAN BESAR"}
					]
				}),
				displayField: "name",
				valueField: "id",
				allowBlank : false,
				editable: false,
				width: 300,
				listeners : {
					specialkey : "onFocusEnter"
				}
		},{
			xtype : "textfield",
			name : "MR_OPSIONAL",
			reference : 'mropthisto',
			plugins: [{
				ptype: 'tip',
				text: 'Nomor Rekam Medis Opsional'
			}],
			width : 300,
			emptyText : "[ NORM OPSIONAL ]",
			allowBlank: false,
			validator: function(value) {
				var form = this.up('form'),
					checkbox = form ? form.down('[reference=cbmropthisto]') : null,
					checked = checkbox ? checkbox.getValue() : false;
				if (!checked) {
					if (!value || value.trim() === "") {
						return "Field ini wajib diisi";
					}
					if (isNaN(value) || Number(value) <= 0) {
						return "NORM harus lebih dari 0";
					}
				} else {
					if (value && isNaN(value)) {
						return "NORM harus berupa angka";
					}
				}
				return true;
			}
		},{
			xtype: "checkbox",
			name: "CHECK_OPSIONAL",
			reference : 'cbmropthisto',
			boxLabel: "Tidak menggunkan NORM External",
			inputValue: 1,
			uncheckedValue: 0,
			width: 300,
			listeners: {
				change: function(cb, checked) {
					var form = cb.up('form'),
						mrField = form.down('[reference=mropthisto]');
					if (mrField) {
						mrField.allowBlank = checked;
						mrField.validate();
					}
				}
			}
		},{
			reference:'terminologi',
			bind: {
				readOnly: '{formConfig.viewOnly}'
			},
			fieldLabel: 'TERMINOLOGI',
			labelWidth: 220,
			xtype: 'terminologipa-combo',
			emptyText: '[ terminologi ]',
			name: 'TERMINOLOGI'
		},{
				xtype: "fieldcontainer",
				layout: "hbox",
				items: [{
					reference: "ckodelayanan",
					xtype : "combobox",
					emptyText : "[ Masukan Deskripsi ]",
					queryMode : "remote",
					bind : {
						store : "{jenisLayananPa}"
					}, 
					width: '90%',
					hideTrigger : true,
					margin: "0 5 0 0",
					typeAhead : true,
					queryParam : "QUERY",
					//displayField : "STR",
					valueField : "ID",
					minChars : 2,
					tpl : Ext.create("Ext.XTemplate", '<tpl for=".">', '<div class="x-boundlist-item"><strong>{REFERENSI.PEMERIKSAAN.DESKRIPSI}</strong> - <strong>{JENIS_LAYANAN}</strong> - {DEFINISI_OPERASIONAL_1} - {DEFINISI_OPERASIONAL_2} - <strong>{REFERENSI.KODELAYANAN.KODE_LAYANAN}</strong> - <strong>{REFERENSI.KODELAYANAN.SKOR}</strong></div>', "</tpl>"),
					displayTpl : Ext.create("Ext.XTemplate", '<tpl for=".">', "{REFERENSI.PEMERIKSAAN.DESKRIPSI} - {JENIS_LAYANAN} - {REFERENSI.KODELAYANAN.KODE_LAYANAN} - {REFERENSI.KODELAYANAN.SKOR}", "</tpl>"),
				},{
					xtype: "button"
					, iconCls: "x-fa fa-plus"
					, handler: "onTambahScor"
				}]
		},{
				xtype: "fieldcontainer"
				, layout: "hbox"
				, items: [  {
					xtype : "detil-scorring-pa",
					width: '100%',
					reference : "detilscorring"
				}]	
		},{
				xtype : "textfield",
				name : "NOMOR_LAB",
				reference : 'nomorhistopalogi',
				width : 300,
				emptyText : "No. Histopalogi",
				readOnly : true
		}
	],
	buttons : [{
			text : "Buat Histopatologi",
			handler : "onSimpan"
		}
	],
	load : function (b) {
		var d = this,
            model = Ext.create('data.model.HasilPa',{}),
            c = d.getViewModel().get("store");
			d.getViewModel().set("tglmax",Ext.Date.add(d.getSysdate(), Ext.Date.DAY, 1));
		if (c) {
			if(b){
				//console.log('bbbbbb');
				//console.log(b);
				d.getViewModel().set("nokun",b.KUNJUNGAN);
				d.getViewModel().set("norm",b.NORM);
				/* c.removeAll();
				c.queryParams = {
					KUNJUNGAN : b.KUNJUNGAN
				};
				c.load({
					callback : function (h, g, i) {
						if(i){
							var rec = c.getAt(0);
							d.getViewModel().set("record",rec);
							model.set(rec.data);
							model.set('TANGGAL_LAB', d.getSysdate());
							d.getViewModel().set("nokun",b.KUNJUNGAN);
							d.getForm().loadRecord(model);
			
						}else{
							d.getViewModel().set("norm",b.NORM);
							d.getViewModel().set("nomor",b.NOMOR_LAB);
							d.getViewModel().set("nokun",b.KUNJUNGAN);
							d.getForm().loadRecord(model);
						}
					}
				}); */
			}
        }
    }
});